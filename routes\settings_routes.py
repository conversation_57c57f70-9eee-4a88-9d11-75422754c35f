"""
Settings-related routes for the Graphiti application.
"""

from fastapi import APIRouter, HTTPException, Depends, status, Body
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

from utils.config import get_available_models, update_config
from utils.logging_utils import get_logger
from utils.dependencies import (
    get_settings as get_app_settings,
    get_llm_config,
    get_database_config,
    get_embedding_config,
    get_ocr_config
)

# Define models for settings updates
class LLMSettings(BaseModel):
    provider: str
    model: str
    api_key: Optional[str] = None

    class Config:
        extra = "allow"

class EmbeddingSettings(BaseModel):
    provider: str = "ollama"
    model: str = "snowflake-arctic-embed2"
    use_local: bool = True
    chunk_size: int = 1200
    chunk_overlap: int = 0
    recursive_chunking: bool = True

    class Config:
        extra = "allow"

class DatabaseSettings(BaseModel):
    host: str
    port: int

    class Config:
        extra = "allow"

class SystemSettings(BaseModel):
    max_file_size: int = 50
    max_parallel_processes: int = 4
    log_level: str = "INFO"

    class Config:
        extra = "allow"

class AllSettings(BaseModel):
    llm_settings: Optional[LLMSettings] = None
    embedding_settings: Optional[EmbeddingSettings] = None
    database_settings: Optional[DatabaseSettings] = None
    system_settings: Optional[SystemSettings] = None

    class Config:
        # Allow extra fields to be included in the request
        extra = "allow"

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["settings"])

async def get_dynamic_available_models() -> Dict[str, Any]:
    """
    Get available models dynamically from various providers.

    Returns:
        Dictionary of available models by provider
    """
    models = {
        "openrouter": [],
        "ollama": [],
        "openai": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o-mini"],
        "gemini": ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"],
        "mistral": ["mistral-large-latest", "mistral-medium-latest", "mistral-small-latest"]
    }

    # Get OpenRouter models
    try:
        openrouter_models = await get_openrouter_models()
        if openrouter_models:
            models["openrouter"] = openrouter_models
        else:
            # Fallback to static list
            models["openrouter"] = [
                "meta-llama/llama-4-maverick",
                "anthropic/claude-3-opus-20240229",
                "anthropic/claude-3-sonnet-20240229",
                "anthropic/claude-3-haiku-20240307",
                "google/gemini-1.5-pro-latest",
                "mistralai/mistral-large-latest",
                "qwen3-4b",
                "qwen3-30b-a3b",
                "nvidia/llama-3.3-nemotron-super-49b-v1:free",
                "huggingfaceh4/zephyr-7b-beta",
                "mistralai/mistral-nemo",
                "google/gemma-3-27b-it"
            ]
    except Exception as e:
        logger.warning(f"Failed to fetch OpenRouter models: {e}")
        models["openrouter"] = [
            "meta-llama/llama-4-maverick",
            "anthropic/claude-3-opus-20240229",
            "anthropic/claude-3-sonnet-20240229",
            "google/gemini-1.5-pro-latest",
            "mistralai/mistral-large-latest"
        ]

    # Get Ollama models
    try:
        ollama_models = await get_ollama_models()
        if ollama_models:
            models["ollama"] = ollama_models
        else:
            # Fallback to static list
            models["ollama"] = ["medllama3-v20", "llama3-8b", "mistral-7b", "gemma-7b", "snowflake-arctic-embed2"]
    except Exception as e:
        logger.warning(f"Failed to fetch Ollama models: {e}")
        models["ollama"] = ["medllama3-v20", "llama3-8b", "mistral-7b", "gemma-7b", "snowflake-arctic-embed2"]

    return models

async def get_openrouter_models() -> List[str]:
    """
    Fetch available models from OpenRouter API.

    Returns:
        List of available OpenRouter model names
    """
    try:
        import httpx
        import os

        api_key = os.environ.get('OPEN_ROUTER_API_KEY')
        if not api_key:
            logger.warning("OpenRouter API key not found")
            return []

        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://openrouter.ai/api/v1/models",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "HTTP-Referer": "https://localhost:9753",
                    "X-Title": "Graphiti Knowledge Graph"
                },
                timeout=10.0
            )

            if response.status_code == 200:
                data = response.json()
                models = []
                for model in data.get("data", []):
                    model_id = model.get("id", "")
                    if model_id:
                        models.append(model_id)

                logger.info(f"Fetched {len(models)} models from OpenRouter")
                return models[:50]  # Limit to first 50 models
            else:
                logger.warning(f"OpenRouter API returned status {response.status_code}")
                return []

    except Exception as e:
        logger.error(f"Error fetching OpenRouter models: {e}")
        return []

async def get_ollama_models() -> List[str]:
    """
    Fetch available models from Ollama API.

    Returns:
        List of available Ollama model names
    """
    try:
        import httpx
        import os

        ollama_base_url = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{ollama_base_url}/api/tags",
                timeout=5.0
            )

            if response.status_code == 200:
                data = response.json()
                models = []
                for model in data.get("models", []):
                    model_name = model.get("name", "")
                    if model_name:
                        models.append(model_name)

                logger.info(f"Fetched {len(models)} models from Ollama")
                return models
            else:
                logger.warning(f"Ollama API returned status {response.status_code}")
                return []

    except Exception as e:
        logger.warning(f"Error fetching Ollama models (Ollama may not be running): {e}")
        return []

@router.get("/settings/models")
async def get_available_models_endpoint():
    """
    Get available models from all providers.

    Returns:
        Dictionary of available models by provider
    """
    try:
        models = await get_dynamic_available_models()
        return {"available_models": models}
    except Exception as e:
        logger.error(f"Error getting available models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting available models: {str(e)}"
        )

@router.get("/ollama/models")
async def get_ollama_models_endpoint():
    """
    Get available models from Ollama.

    Returns:
        List of available Ollama models
    """
    try:
        models = await get_ollama_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"Error getting Ollama models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting Ollama models: {str(e)}"
        )

@router.get("/openrouter/models")
async def get_openrouter_models_endpoint():
    """
    Get available models from OpenRouter.

    Returns:
        List of available OpenRouter models
    """
    try:
        models = await get_openrouter_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"Error getting OpenRouter models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting OpenRouter models: {str(e)}"
        )

@router.get("/settings")
async def get_settings(
    settings: Dict[str, Any] = Depends(get_app_settings),
    llm_config: Dict[str, Any] = Depends(get_llm_config),
    database_config: Dict[str, Any] = Depends(get_database_config),
    embedding_config: Dict[str, Any] = Depends(get_embedding_config),
    ocr_config: Dict[str, Any] = Depends(get_ocr_config)
):
    """
    Get all settings for the application.

    Args:
        settings: Application settings
        llm_config: LLM configuration
        database_config: Database configuration
        embedding_config: Embedding configuration
        ocr_config: OCR configuration

    Returns:
        Application settings
    """
    try:
        # Get available models dynamically
        available_models = await get_dynamic_available_models()

        # Get LLM settings
        llm_settings = {
            "provider": llm_config.get("provider", "openrouter"),
            "model": llm_config.get("model", "meta-llama/llama-4-maverick")
        }

        # Get database settings
        database_settings = {
            "type": "FalkorDB",
            "host": database_config.get("host", "localhost"),
            "port": database_config.get("port", 6379),
            "graph_name": database_config.get("graph", "graphiti")
        }

        # Get embedding settings
        embedding_settings = {
            "provider": embedding_config.get("provider", "openai"),
            "model": embedding_config.get("model", "text-embedding-3-small"),
            "use_local": embedding_config.get("use_local", False),
            "chunking_method": "recursive" if embedding_config.get("recursive_chunking", True) else "simple",
            "chunk_size": embedding_config.get("chunk_size", 1200),
            "chunk_overlap": embedding_config.get("chunk_overlap", 0),
            "recursive_chunking": embedding_config.get("recursive_chunking", True)
        }

        # Get OCR settings
        ocr_settings = {
            "provider": ocr_config.get("provider", "mistral"),
            "model": ocr_config.get("model", "mistral-ocr-latest"),
            "use_mistral": ocr_config.get("provider", "mistral") == "mistral"
        }

        return {
            "llm": llm_settings,
            "database": database_settings,
            "embedding": embedding_settings,
            "ocr": ocr_settings,
            "available_models": available_models
        }

    except Exception as e:
        logger.error(f"Error getting settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting settings: {str(e)}"
        )

@router.post("/settings/llm")
async def update_llm_settings(settings: LLMSettings):
    """
    Update LLM settings.

    Args:
        settings: LLM settings

    Returns:
        Updated LLM settings
    """
    try:
        # Update config
        config_updates = {
            "QA_LLM_PROVIDER": settings.provider,
            "QA_LLM_MODEL": settings.model
        }

        # Update USE_LOCAL_LLM if provided
        if hasattr(settings, 'use_local'):
            config_updates["USE_LOCAL_LLM"] = str(settings.use_local).lower()

        # Update the .env file
        update_env_file(config_updates)

        return {
            "success": True,
            "provider": settings.provider,
            "model": settings.model,
            "use_local": getattr(settings, 'use_local', False)
        }

    except Exception as e:
        logger.error(f"Error updating LLM settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating LLM settings: {str(e)}"
        )

@router.post("/settings/embedding")
async def update_embedding_settings(settings: EmbeddingSettings):
    """
    Update embedding settings.

    Args:
        settings: Embedding settings

    Returns:
        Updated embedding settings
    """
    try:
        # Update config
        config_updates = {
            "EMBEDDING_PROVIDER": settings.provider,
            "EMBEDDING_MODEL": settings.model,
            "USE_LOCAL_EMBEDDINGS": str(settings.use_local).lower(),
            "CHUNK_SIZE": str(settings.chunk_size),
            "CHUNK_OVERLAP": str(settings.chunk_overlap),
            "RECURSIVE_CHUNKING": str(settings.recursive_chunking).lower()
        }

        # Update the .env file
        update_env_file(config_updates)

        return {
            "success": True,
            "provider": settings.provider,
            "model": settings.model,
            "use_local": settings.use_local,
            "chunk_size": settings.chunk_size,
            "chunk_overlap": settings.chunk_overlap,
            "recursive_chunking": settings.recursive_chunking
        }

    except Exception as e:
        logger.error(f"Error updating embedding settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating embedding settings: {str(e)}"
        )

@router.post("/settings/database")
async def update_database_settings(settings: DatabaseSettings):
    """
    Update database settings.

    Args:
        settings: Database settings

    Returns:
        Updated database settings
    """
    try:
        # Update config
        config_updates = {
            "FALKORDB_HOST": settings.host,
            "FALKORDB_PORT": str(settings.port)
        }

        # Update the .env file
        update_env_file(config_updates)

        return {
            "success": True,
            "host": settings.host,
            "port": settings.port
        }

    except Exception as e:
        logger.error(f"Error updating database settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating database settings: {str(e)}"
        )

@router.post("/settings")
async def update_all_settings(settings: dict):
    """
    Update all settings.

    Args:
        settings: All settings as a dictionary

    Returns:
        Updated settings
    """
    try:
        # Update LLM settings
        if 'llm_settings' in settings and settings['llm_settings']:
            llm_settings = settings['llm_settings']
            config_updates = {
                "QA_LLM_PROVIDER": llm_settings.get('provider', 'openrouter'),
                "QA_LLM_MODEL": llm_settings.get('model', 'meta-llama/llama-4-maverick')
            }

            # Add LLM parameters if provided
            if 'temperature' in llm_settings:
                config_updates["QA_LLM_TEMPERATURE"] = str(llm_settings['temperature'])
            if 'max_tokens' in llm_settings:
                config_updates["QA_LLM_MAX_TOKENS"] = str(llm_settings['max_tokens'])
            if 'top_k' in llm_settings:
                config_updates["QA_LLM_TOP_K"] = str(llm_settings['top_k'])
            if 'top_p' in llm_settings:
                config_updates["QA_LLM_TOP_P"] = str(llm_settings['top_p'])

            # Update API key if provided
            if 'api_key' in llm_settings and llm_settings['api_key']:
                if llm_settings.get('provider') == "openrouter":
                    config_updates["OPENROUTER_API_KEY"] = llm_settings['api_key']
                elif llm_settings.get('provider') == "openai":
                    config_updates["OPENAI_API_KEY"] = llm_settings['api_key']

            # Update USE_LOCAL_LLM if provided
            if 'use_local' in llm_settings:
                config_updates["USE_LOCAL_LLM"] = str(llm_settings['use_local']).lower()

            # Update the .env file
            update_env_file(config_updates)

        # Update embedding settings
        if 'embedding_settings' in settings and settings['embedding_settings']:
            embedding_settings = settings['embedding_settings']
            config_updates = {
                "EMBEDDING_PROVIDER": embedding_settings.get('provider', 'ollama'),
                "EMBEDDING_MODEL": embedding_settings.get('model', 'snowflake-arctic-embed2'),
                "USE_LOCAL_EMBEDDINGS": str(embedding_settings.get('use_local', True)).lower(),
                "CHUNK_SIZE": str(embedding_settings.get('chunk_size', 1200)),
                "CHUNK_OVERLAP": str(embedding_settings.get('chunk_overlap', 0)),
                "RECURSIVE_CHUNKING": str(embedding_settings.get('recursive_chunking', True)).lower()
            }

            # Update the .env file
            update_env_file(config_updates)

        # Update database settings
        if 'database_settings' in settings and settings['database_settings']:
            database_settings = settings['database_settings']
            config_updates = {
                "FALKORDB_HOST": database_settings.get('host', 'localhost'),
                "FALKORDB_PORT": str(database_settings.get('port', 6379))
            }

            # Update the .env file
            update_env_file(config_updates)

        # Update system settings
        if 'system_settings' in settings and settings['system_settings']:
            system_settings = settings['system_settings']
            config_updates = {
                "MAX_FILE_SIZE": str(system_settings.get('max_file_size', 50)),
                "MAX_PARALLEL_PROCESSES": str(system_settings.get('max_parallel_processes', 4)),
                "LOG_LEVEL": system_settings.get('log_level', 'INFO')
            }

            # Update the .env file
            update_env_file(config_updates)

        # Handle direct field updates (for Q&A interface)
        direct_config_updates = {}

        # Check for direct LLM provider and model updates
        if 'qa_llm_provider' in settings:
            direct_config_updates["QA_LLM_PROVIDER"] = settings['qa_llm_provider']
        if 'qa_llm_model' in settings:
            direct_config_updates["QA_LLM_MODEL"] = settings['qa_llm_model']

        # Check for direct LLM parameter updates
        if 'qa_llm_temperature' in settings:
            direct_config_updates["QA_LLM_TEMPERATURE"] = str(settings['qa_llm_temperature'])
        if 'qa_llm_max_tokens' in settings:
            direct_config_updates["QA_LLM_MAX_TOKENS"] = str(settings['qa_llm_max_tokens'])
        if 'qa_llm_top_k' in settings:
            direct_config_updates["QA_LLM_TOP_K"] = str(settings['qa_llm_top_k'])
        if 'qa_llm_top_p' in settings:
            direct_config_updates["QA_LLM_TOP_P"] = str(settings['qa_llm_top_p'])

        # Update .env file if there are direct updates
        if direct_config_updates:
            update_env_file(direct_config_updates)

        return {
            "success": True,
            "message": "Settings updated successfully"
        }

    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating settings: {str(e)}"
        )

@router.post("/settings/reset")
async def reset_settings():
    """
    Reset settings to defaults.

    Returns:
        Default settings
    """
    try:
        # Default settings
        default_settings = {
            "QA_LLM_PROVIDER": "openrouter",
            "QA_LLM_MODEL": "meta-llama/llama-4-maverick",
            "EMBEDDING_PROVIDER": "ollama",
            "EMBEDDING_MODEL": "snowflake-arctic-embed2",
            "USE_LOCAL_EMBEDDINGS": "true",
            "CHUNK_SIZE": "1200",
            "CHUNK_OVERLAP": "0",
            "RECURSIVE_CHUNKING": "true",
            "FALKORDB_HOST": "localhost",
            "FALKORDB_PORT": "6379",
            "MAX_FILE_SIZE": "50",
            "MAX_PARALLEL_PROCESSES": "4",
            "LOG_LEVEL": "INFO"
        }

        # Update the .env file
        update_env_file(default_settings)

        # Return default settings directly since we just set them
        return {
            "success": True,
            "message": "Settings reset to defaults successfully",
            "llm": {
                "provider": "openrouter",
                "model": "meta-llama/llama-4-maverick"
            },
            "database": {
                "type": "FalkorDB",
                "host": "localhost",
                "port": 6379,
                "graph_name": "graphiti"
            },
            "embedding": {
                "provider": "ollama",
                "model": "snowflake-arctic-embed2",
                "use_local": True,
                "chunk_size": 1200,
                "chunk_overlap": 0,
                "recursive_chunking": True
            },
            "ocr": {
                "provider": "mistral",
                "model": "mistral-ocr-latest"
            }
        }

    except Exception as e:
        logger.error(f"Error resetting settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting settings: {str(e)}"
        )

@router.post("/settings/test-connection")
async def test_connection(settings: DatabaseSettings):
    """
    Test database connection.

    Args:
        settings: Database settings

    Returns:
        Connection status
    """
    try:
        # Test FalkorDB connection
        from database.falkordb_adapter import FalkorDBAdapter

        falkordb = FalkorDBAdapter(
            host=settings.host,
            port=settings.port,
            password=None,
            graph="graphiti"
        )

        falkordb_result = falkordb.execute_cypher("RETURN 1")
        falkordb_connected = falkordb_result is not None

        # Test Redis connection
        import redis.asyncio as redis

        redis_client = redis.Redis(
            host=settings.host,
            port=6380,  # Use Redis port
            decode_responses=True
        )

        redis_result = await redis_client.ping()
        redis_connected = redis_result

        return {
            "success": falkordb_connected and redis_connected,
            "falkordb_connected": falkordb_connected,
            "redis_connected": redis_connected
        }

    except Exception as e:
        logger.error(f"Error testing connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error testing connection: {str(e)}"
        )

def update_env_file(updates: Dict[str, str]):
    """
    Update the .env file with new values.

    Args:
        updates: Dictionary of key-value pairs to update
    """
    try:
        # Read the current .env file
        with open(".env", "r") as f:
            lines = f.readlines()

        # Update the values
        updated_lines = []
        for line in lines:
            updated = False
            for key, value in updates.items():
                if line.startswith(f"{key}="):
                    updated_lines.append(f"{key}={value}\n")
                    updated = True
                    break

            if not updated:
                updated_lines.append(line)

        # Write the updated .env file
        with open(".env", "w") as f:
            f.writelines(updated_lines)

    except Exception as e:
        logger.error(f"Error updating .env file: {str(e)}")
        raise Exception(f"Error updating .env file: {str(e)}")

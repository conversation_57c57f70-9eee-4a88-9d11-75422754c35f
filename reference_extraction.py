"""
Module for extracting references from scientific documents
"""

import os
import re
import logging
import asyncio
import json
import csv
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import PyPDF2
from dotenv import load_dotenv
import fitz  # PyMuPDF

# Import LLM client if available
try:
    from mistralai import Mistral
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from utils.open_router_client import OpenRouterClient
    OPENROUTER_AVAILABLE = True
except ImportError:
    OPENROUTER_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get API keys
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
MISTRAL_API_KEY = os.environ.get('MISTRAL_API_KEY')
OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY') or os.environ.get('OPEN_ROUTER_API_KEY')

class ReferenceExtractor:
    """Class for extracting references from scientific documents"""

    def __init__(self, llm_provider: str = 'openai'):
        """
        Initialize the reference extractor

        Args:
            llm_provider: The LLM provider to use ('openai', 'mistral', 'openrouter', or None)
        """
        self.llm_provider = llm_provider

        # Initialize LLM clients
        self.openai_client = None
        self.mistral_client = None
        self.openrouter_client = None

        if llm_provider == 'openai' and OPENAI_AVAILABLE and OPENAI_API_KEY:
            try:
                # Initialize with a custom httpx client to avoid proxies issue
                import httpx
                # Create a custom httpx client with appropriate settings
                http_client = httpx.Client(timeout=60.0)
                self.openai_client = openai.OpenAI(api_key=OPENAI_API_KEY, http_client=http_client)
                logger.info("Initialized OpenAI client for reference extraction with custom httpx client")
            except Exception as e:
                logger.error(f"Error initializing OpenAI client: {e}")
                self.openai_client = None
        elif llm_provider == 'mistral' and MISTRAL_AVAILABLE and MISTRAL_API_KEY:
            try:
                # Initialize Mistral client with proper parameters
                self.mistral_client = Mistral(api_key=MISTRAL_API_KEY)
                logger.info("Initialized Mistral client for reference extraction")
            except TypeError as e:
                # Handle potential parameter issues
                logger.warning(f"TypeError initializing Mistral client: {e}, trying alternative initialization")
                try:
                    # Try alternative initialization if needed
                    import httpx
                    http_client = httpx.Client(timeout=60.0)
                    self.mistral_client = Mistral(api_key=MISTRAL_API_KEY, client=http_client)
                    logger.info("Initialized Mistral client with custom httpx client")
                except Exception as e2:
                    logger.error(f"Error in alternative Mistral client initialization: {e2}")
                    self.mistral_client = None
            except Exception as e:
                logger.error(f"Error initializing Mistral client: {e}")
                self.mistral_client = None
        elif llm_provider == 'openrouter' and OPENROUTER_AVAILABLE and OPENROUTER_API_KEY:
            try:
                # Initialize OpenRouter client
                model = os.environ.get('REFERENCE_EXTRACTION_MODEL', 'meta-llama/llama-4-maverick')
                self.openrouter_client = OpenRouterClient(api_key=OPENROUTER_API_KEY, model=model)
                logger.info(f"Initialized OpenRouter client for reference extraction with model: {model}")
            except Exception as e:
                logger.error(f"Error initializing OpenRouter client: {e}")
                self.openrouter_client = None
        elif llm_provider:
            logger.warning(f"LLM provider '{llm_provider}' not available or API key not set")

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract full text from a PDF file

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Extracted text
        """
        try:
            # Use PyMuPDF (fitz) for better text extraction
            doc = fitz.open(pdf_path)
            text = ""

            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text()
                text += f"\n--- Page {page_num + 1} ---\n"

            doc.close()
            return text

        except Exception as e:
            logger.error(f"Error extracting text with PyMuPDF from {pdf_path}: {e}")

            # Fall back to PyPDF2
            try:
                with open(pdf_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    text = ""

                    for page_num in range(len(reader.pages)):
                        page = reader.pages[page_num]
                        text += page.extract_text() + f"\n--- Page {page_num + 1} ---\n"

                    return text

            except Exception as e2:
                logger.error(f"Error extracting text with PyPDF2 from {pdf_path}: {e2}")
                return ""

    def find_reference_section(self, text: str) -> str:
        """
        Find the reference section in the document text.
        If a dedicated reference section is not found, returns the entire document text
        to allow for extraction of references from anywhere in the document.

        Args:
            text: Full document text

        Returns:
            Text of the reference section or the entire document
        """
        # Enhanced section headers for references
        reference_headers = [
            r"References\s*\n",
            r"REFERENCES\s*\n",
            r"Bibliography\s*\n",
            r"BIBLIOGRAPHY\s*\n",
            r"Works Cited\s*\n",
            r"WORKS CITED\s*\n",
            r"Literature Cited\s*\n",
            r"LITERATURE CITED\s*\n",
            r"\nReferences\s*\n",
            r"\nREFERENCES\s*\n",
            r"\d+\.\s*References\s*\n",
            r"\d+\.\s*REFERENCES\s*\n",
            r"References for Previous Slide\s*\n",
            r"References for previous slide\s*\n",
            r"References for Slide\s*\n",
            r"References for slide\s*\n",
            r"References for Slides\s*\n",
            r"References for slides\s*\n",
            r"References for This Slide\s*\n",
            r"References for this slide\s*\n",
            r"Sources\s*\n",
            r"SOURCES\s*\n",
            r"Citations\s*\n",
            r"CITATIONS\s*\n",
            r"\nReference List\s*\n",
            r"\nREFERENCE LIST\s*\n",
            r"Reference List\s*\n",
            r"REFERENCE LIST\s*\n",
            r"\nReferences Cited\s*\n",
            r"\nREFERENCES CITED\s*\n",
            r"References Cited\s*\n",
            r"REFERENCES CITED\s*\n",
            r"\n•\s*[A-Z]"  # Bullet point pattern for references
        ]

        # Try to find the reference section
        reference_section = ""
        for header_pattern in reference_headers:
            match = re.search(header_pattern, text)
            if match:
                start_pos = match.start()
                # Try to find the next section header or end of document
                next_section_match = re.search(r"\n\d+\.\s*[A-Z][a-zA-Z\s]+\n|\n[A-Z][A-Z\s]+\n", text[start_pos+10:])

                if next_section_match:
                    end_pos = start_pos + 10 + next_section_match.start()
                    reference_section = text[start_pos:end_pos]
                else:
                    # If no next section found, take the rest of the document
                    reference_section = text[start_pos:]

                break

        # If no reference section found, try to find by looking for numbered references
        if not reference_section:
            # Look for patterns like [1], [2], etc. or (1), (2), etc.
            ref_matches = list(re.finditer(r"(?:\[\d+\]|\(\d+\))\s+[A-Z]", text))
            if len(ref_matches) > 5:  # If we find multiple references
                # Find the first one and take from there
                start_pos = ref_matches[0].start()
                reference_section = text[start_pos:]

        # If no reference section found, look for common citation patterns throughout the document
        if not reference_section:
            # Look for DOI patterns
            doi_matches = list(re.finditer(r"doi:\s*10\.\d{4}/[\w\.-]+", text, re.IGNORECASE))
            # Look for PMID patterns
            pmid_matches = list(re.finditer(r"PMID:\s*\d+", text, re.IGNORECASE))
            # Look for journal citation patterns
            journal_matches = list(re.finditer(r"[A-Z][a-z]+\s+[A-Z][a-z]+.*?\d{4}.*?;.*?\d+\s*\(.*?\):\s*\d+", text))

            # If we find multiple citation patterns, use the entire document
            if len(doi_matches) > 2 or len(pmid_matches) > 2 or len(journal_matches) > 2:
                logger.info(f"Found citation patterns throughout document: {len(doi_matches)} DOIs, {len(pmid_matches)} PMIDs, {len(journal_matches)} journal citations")
                return text

        # If we found a dedicated reference section, return it
        if reference_section:
            return reference_section

        # Otherwise, return the entire document to allow for extraction of references from anywhere
        logger.info("No dedicated reference section found, using entire document for reference extraction")
        return text

    def extract_references_with_regex(self, text: str) -> List[str]:
        """
        Extract references using regex patterns

        Args:
            text: Text to extract references from

        Returns:
            List of extracted references
        """
        references = []

        # Find the reference section
        reference_section = self.find_reference_section(text)
        if not reference_section:
            logger.warning("Could not find reference section")
            return references

        # Define improved regex patterns for reference extraction
        JOURNAL_CITATION_PATTERN = r"([A-Z][a-z]+(?:,?\s+(?:and|&)?\s*[A-Z][a-z]+)*,?\s+(?:et\s+al\.?)?,?\s+[A-Za-z\s]+\.\s*\d{4}(?:;|\s+)(?:\d+)?(?:\((?:\d+)\))?:(?:\d+-\d+)?)"
        AUTHOR_YEAR_PATTERN = r"([A-Z][a-z]+(?:,?\s+(?:and|&)?\s*[A-Z][a-z]+)*,?\s+(?:et\s+al\.?)?,?\s+\(\d{4}\).*?)(?=[A-Z][a-z]+(?:,?\s+(?:and|&)?\s*[A-Z][a-z]+)*,?\s+(?:et\s+al\.?)?,?\s+\(\d{4}\)|$)"
        DOI_PATTERN = r"((?:[A-Z][a-z]+\s+)+et\s+al\.?\s+.*?doi:\s*10\.\d{4}/[\w\.-]+)"
        PMID_PATTERN = r"((?:[A-Z][a-z]+\s+)+et\s+al\.?\s+.*?PMID:\s*\d+)"

        # Define patterns for false positive references
        FALSE_POSITIVE_PATTERNS = [
            r"--- Page \d+ ---",  # Page markers
            r"Contents Naturopathic Considerations and Therapeutic Goals",  # Table of contents
            r"Overview_+",  # Table of contents formatting
            r"Aetiology and Pathophysiology_+",  # Table of contents formatting
            r"Risk Factors_+",  # Table of contents formatting
            r"Signs and Symptoms_+",  # Table of contents formatting
            r"Diagnosis and Tests_+",  # Table of contents formatting
            r"Differential Diagnosis_+",  # Table of contents formatting
            r"Associated Systems and Factors_+",  # Table of contents formatting
            r"Herbal Medicine_+",  # Table of contents formatting
            r"Nutritional Medicine_+",  # Table of contents formatting
            r"Dietary Considerations_+",  # Table of contents formatting
            r"Lifestyle Considerations_+",  # Table of contents formatting
            r"Further Resources_+",  # Table of contents formatting
            r"References_+",  # Table of contents formatting
        ]

        # Function to check if a reference is a false positive
        def is_false_positive(ref: str) -> bool:
            # Check if the reference contains any of the false positive patterns
            for pattern in FALSE_POSITIVE_PATTERNS:
                if re.search(pattern, ref):
                    return True

            # Check if the reference contains multiple page markers
            if ref.count("--- Page") > 1:
                return True

            # Check if the reference is too long (likely document content)
            if len(ref) > 500:
                return True

            return False

        # Try different reference patterns

        # Pattern 1: Numbered references like [1] or (1) or 1.
        numbered_refs = re.findall(r"(?:\[\d+\]|\(\d+\)|\d+\.)\s+(.*?)(?=(?:\[\d+\]|\(\d+\)|\d+\.)|$)", reference_section, re.DOTALL)
        if numbered_refs:
            # Clean up the references
            for ref in numbered_refs:
                # Remove line breaks and extra whitespace
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref):  # Minimum length to be a valid reference
                    references.append(clean_ref)

        # Pattern 2: Journal citations
        journal_refs = re.findall(JOURNAL_CITATION_PATTERN, reference_section)
        if journal_refs:
            for ref in journal_refs:
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref) and clean_ref not in references:
                    references.append(clean_ref)

        # Pattern 3: Author-year references
        author_year_refs = re.findall(AUTHOR_YEAR_PATTERN, reference_section, re.DOTALL)
        if author_year_refs:
            for ref in author_year_refs:
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref) and clean_ref not in references:
                    references.append(clean_ref)

        # Pattern 4: DOI references
        doi_refs = re.findall(DOI_PATTERN, reference_section)
        if doi_refs:
            for ref in doi_refs:
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref) and clean_ref not in references:
                    references.append(clean_ref)

        # Pattern 5: PMID references
        pmid_refs = re.findall(PMID_PATTERN, reference_section)
        if pmid_refs:
            for ref in pmid_refs:
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref) and clean_ref not in references:
                    references.append(clean_ref)

        # Pattern 6: Look for specific journal citation patterns
        # This pattern looks for Author. Journal. Year;Volume(Issue):Pages
        specific_journal_refs = re.findall(r"([A-Z][a-z]+(?:,?\s+[A-Z][a-z]+)*(?:\s+et\s+al\.?)?\.\s+[A-Za-z\s]+\.\s+\d{4}(?:;|\s+)(?:\d+)?(?:\((?:\d+)\))?:(?:\d+-\d+)?)", reference_section)
        if specific_journal_refs:
            for ref in specific_journal_refs:
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref) and clean_ref not in references:
                    references.append(clean_ref)

        # Pattern 7: Bullet point references (•, *, -, etc.)
        bullet_refs = re.findall(r"(?:•|\*|\-)\s+(.*?)(?=(?:•|\*|\-)\s+|$)", reference_section, re.DOTALL)
        if bullet_refs:
            for ref in bullet_refs:
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref) and clean_ref not in references:
                    references.append(clean_ref)

        # Pattern 8: URL references
        url_refs = re.findall(r"((?:https?://|www\.)[^\s]+\.[^\s]+(?:\.[^\s]+)?(?:/[^\s]*)?)", reference_section)
        if url_refs:
            for url in url_refs:
                # Try to find context around the URL (up to 100 chars before and after)
                url_pos = reference_section.find(url)
                if url_pos >= 0:
                    start_pos = max(0, url_pos - 100)
                    end_pos = min(len(reference_section), url_pos + len(url) + 100)
                    context = reference_section[start_pos:end_pos]
                    clean_ref = re.sub(r"\s+", " ", context).strip()
                    if clean_ref and len(clean_ref) > 20 and not is_false_positive(clean_ref) and clean_ref not in references:
                        references.append(clean_ref)

        # If still no references found, split by common separators
        if not references and len(reference_section) > 100:
            # Split by line breaks that are followed by patterns that look like the start of a reference
            potential_refs = re.split(r"\n(?=[A-Z][a-z]+,|\[\d+\]|\(\d+\)|\d+\.)", reference_section)
            for ref in potential_refs:
                clean_ref = re.sub(r"\s+", " ", ref).strip()
                if clean_ref and len(clean_ref) > 40 and not is_false_positive(clean_ref):  # Longer minimum to avoid false positives
                    references.append(clean_ref)

        # Final check for false positives
        filtered_references = []
        for ref in references:
            # Check if the reference looks like a bibliographic reference
            # Most references have authors, year, and journal
            has_author_pattern = re.search(r"[A-Z][a-z]+\s+[A-Z][a-z]*", ref)
            has_year_pattern = re.search(r"\d{4}", ref)
            has_journal_pattern = re.search(r"[A-Z][a-z]+\s+[A-Za-z\s]+\.", ref)

            # If it's a long text without these patterns, it's likely not a reference
            if len(ref) > 200 and not (has_author_pattern and has_year_pattern and has_journal_pattern):
                logger.debug(f"Filtered out false positive reference: {ref[:50]}...")
                continue

            filtered_references.append(ref)

        return filtered_references

    async def extract_references_with_llm(self, text: str, max_tokens: int = 4000) -> List[Dict[str, str]]:
        """
        Extract and parse references using LLM

        Args:
            text: Text to extract references from
            max_tokens: Maximum tokens to send to the LLM

        Returns:
            List of structured reference dictionaries
        """
        if not self.openai_client and not self.mistral_client and not self.openrouter_client:
            logger.warning("No LLM client available for reference extraction")
            return []

        # Find the reference section
        reference_section = self.find_reference_section(text)
        if not reference_section:
            logger.warning("Could not find reference section for LLM extraction")
            # Try using the last part of the document
            reference_section = text[-15000:] if len(text) > 15000 else text

        # Truncate to avoid token limits
        if len(reference_section) > max_tokens:
            reference_section = reference_section[:max_tokens]

        # Create prompt for the LLM
        prompt = f"""
        Extract all bibliographic references from the following text, including references that appear anywhere in the document, not just in a dedicated "References" section.

        Pay special attention to:
        1. Numbered reference lists (e.g., "1. Author et al. Title...")
        2. References following headers like "References", "References for Previous Slide", "Bibliography", etc.
        3. Academic citation formats with authors, year, title, journal, etc.
        4. References with DOIs, PMIDs, or other identifiers
        5. Inline citations that include full reference information
        6. References embedded in slides, presentations, or non-traditional document formats
        7. References that appear in tables, figures, or footnotes
        8. References that use standard scientific citation formats but don't appear in a dedicated section
        9. Incomplete references that are cut off (try to complete them based on context)

        Look for these common reference patterns:
        - Numbered references: [1] or (1) or 1. followed by citation information
        - Author-year citations: (Smith et al., 2020) followed by full reference details
        - References with identifiers: "doi: 10.1234/example" or "PMID: 12345678"
        - Standard academic format: Author(s), title, journal, year, volume, pages
        - References in non-standard locations: slides, headers, footers, or body text
        - Incomplete references: "Author et al. Journal. Year;" (missing volume/pages)

        For incomplete references, try to infer the missing information if possible. For example:
        - If a reference ends with "Journal. Year;" but is missing volume/pages, try to complete it
        - If a reference has authors and journal but no year, try to infer the year from context
        - If a reference has a DOI or PMID, use that to infer other missing information

        For each reference, extract the following fields when available:
        - authors (as a list or string)
        - title
        - year
        - journal/conference/book
        - volume
        - issue
        - pages
        - doi
        - pmid
        - url

        Examples of references to extract:

        ```
        References for Previous Slide
        1. Movafegh A, Alizadeh R, Hajimohamadi F et al. Preoperative oral Passiflora incarnata reduces anxiety in ambulatory surgery patients: a double-blind, placebo-controlled study. Anesth Analg. 2008; 106(6): 1728-1732. doi: 10.1213/ane.0b013e318172c3f9. PMID: 18499602
        2. Aslanargun P, Cuvas O, Dikmen B et al. Passiflora incarnata Linneaus as an anxiolytic before spinal anesthesia. J Anesth. 2012; 26(1): 3944. doi: 10.1007/s00540-011-12656. PMID: 22048283
        ```

        ```
        Inline references:
        Li B, Yang J et al. Clin Res Cardiol. 2020. doi: 10.1007/s00392-020-01626-9. PMID: 32161990 [Epub ahead of print]
        Guan WJ, Ni ZY et al. N Engl J Med. 2020. doi: 10.1056/NEJMoa2002032. PMID: 32109013 [Epub ahead of print]
        ```

        Return the references as a JSON array of objects, with each object containing the fields above.
        If a field is not available, omit it from the object.
        Include the full original reference text in a field called "reference_text".

        Here is the text containing references:

        {reference_section}
        """

        try:
            llm_response = None

            # Use OpenRouter (preferred)
            if self.openrouter_client:
                try:
                    llm_response = self.openrouter_client.generate_completion(
                        system_prompt="You are a scientific reference extraction system.",
                        user_prompt=prompt,
                        temperature=0.3,
                        max_tokens=4000
                    )
                    logger.info("Successfully extracted references using OpenRouter")
                except Exception as e:
                    logger.error(f"Error using OpenRouter for reference extraction: {e}")
                    llm_response = None

            # Use OpenAI
            elif self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are a scientific reference extraction system."},
                        {"role": "user", "content": prompt}
                    ],
                    response_format={"type": "json_object"}
                )
                llm_response = response.choices[0].message.content

            # Use Mistral
            elif self.mistral_client:
                try:
                    # Try the new API format first
                    response = self.mistral_client.chat.complete(
                        model="mistral-large-latest",
                        messages=[
                            {"role": "system", "content": "You are a scientific reference extraction system."},
                            {"role": "user", "content": prompt}
                        ]
                    )
                    llm_response = response.choices[0].message.content
                except (AttributeError, TypeError):
                    # Fall back to old API format
                    try:
                        response = self.mistral_client.chat(
                            model="mistral-large-latest",
                            messages=[
                                {"role": "system", "content": "You are a scientific reference extraction system."},
                                {"role": "user", "content": prompt}
                            ]
                        )
                        llm_response = response.choices[0].message.content
                    except Exception as e:
                        logger.error(f"Error using Mistral API: {e}")
                        llm_response = None

            # Parse the response
            if llm_response:
                try:
                    data = json.loads(llm_response)
                    references = data.get("references", [])
                    if not references and isinstance(data, list):
                        references = data

                    logger.info(f"Extracted {len(references)} references with LLM")
                    return references

                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing LLM response: {e}")
                    return []

        except Exception as e:
            logger.error(f"Error extracting references with LLM: {e}")
            return []

        return []

    async def extract_references(self, pdf_path: str) -> List[Dict[str, Any]]:
        """
        Extract references from a document.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            List of references
        """
        logger.info(f"Extracting references from {pdf_path}")

        # Process the document
        result = await self.process_document(pdf_path)

        if not result.get("success", False):
            logger.warning(f"Failed to extract references from {pdf_path}")
            return []

        # Combine regex and LLM references
        references = []

        # Add regex references
        for ref in result.get("regex_references", []):
            references.append({
                "text": ref,
                "extraction_method": "regex",
                "extraction_timestamp": int(time.time())
            })

        # Add LLM references
        for ref in result.get("llm_references", []):
            if isinstance(ref, dict):
                ref["extraction_method"] = "llm"
                ref["extraction_timestamp"] = int(time.time())
                references.append(ref)
            else:
                references.append({
                    "text": str(ref),
                    "extraction_method": "llm",
                    "extraction_timestamp": int(time.time())
                })

        logger.info(f"Extracted {len(references)} references from {pdf_path}")
        return references

    async def process_document(self, pdf_path: str) -> Dict[str, Any]:
        """
        Process a document to extract references

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Dictionary containing extracted references and metadata
        """
        logger.info(f"Extracting references from {pdf_path}")

        # Extract text from PDF
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            logger.warning(f"No text extracted from {pdf_path}")
            return {"success": False, "error": "No text extracted", "references": []}

        # Extract references with regex
        regex_references = self.extract_references_with_regex(text)

        # Extract references with LLM
        llm_references = []
        if self.openai_client or self.mistral_client:
            llm_references = await self.extract_references_with_llm(text)

        # Combine results
        result = {
            "filename": os.path.basename(pdf_path),
            "file_path": pdf_path,
            "extraction_date": datetime.now().isoformat(),
            "extraction_timestamp": int(time.time()),  # Add timestamp for database storage
            "success": True,
            "regex_references": regex_references,
            "regex_reference_count": len(regex_references),
            "llm_references": llm_references,
            "llm_reference_count": len(llm_references),
            "total_reference_count": len(regex_references) + len(llm_references)
        }

        return result

    def save_references_to_json(self, result: Dict[str, Any], output_path: str = None) -> str:
        """
        Save references to a JSON file

        Args:
            result: Result dictionary with references
            output_path: Path to save the JSON file (if None, will use the document filename)

        Returns:
            Path to the saved JSON file
        """
        if not output_path:
            filename = result.get("filename", "document")
            base_filename = os.path.splitext(filename)[0]
            output_path = f"{base_filename}_references.json"

        try:
            # Convert Path objects to strings
            result_copy = result.copy()
            if "file_path" in result_copy and hasattr(result_copy["file_path"], "__fspath__"):
                result_copy["file_path"] = str(result_copy["file_path"])

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result_copy, f, indent=2, ensure_ascii=False)

            logger.info(f"Saved references to {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error saving references to {output_path}: {e}")
            return None

    def save_references_to_csv(self, result: Dict[str, Any], output_path: str = None) -> str:
        """
        Save references to a CSV file

        Args:
            result: Result dictionary with references
            output_path: Path to save the CSV file (if None, will use the document filename)

        Returns:
            Path to the saved CSV file
        """
        if not output_path:
            filename = result.get("filename", "document")
            base_filename = os.path.splitext(filename)[0]
            output_path = f"{base_filename}_references.csv"

        try:
            # Try to import the enhanced reference export module
            try:
                from reference_export_enhanced import (
                    save_references_to_enhanced_csv,
                    convert_reference_result_to_list
                )
                use_enhanced_export = True
                logger.info("Using enhanced reference export")
            except ImportError:
                use_enhanced_export = False
                logger.info("Enhanced reference export not available, using standard export")

            if use_enhanced_export:
                # Convert result to list of references
                references = convert_reference_result_to_list(result)

                # Save using enhanced export
                return save_references_to_enhanced_csv(references, output_path)
            else:
                # Use standard export (legacy code)
                # Combine all references
                all_references = []

                # Handle different input formats
                llm_references = []
                regex_references = []
                source_document = ""

                if isinstance(result, dict):
                    llm_references = result.get("llm_references", [])
                    regex_references = result.get("regex_references", [])
                    source_document = result.get("filename", "")
                elif isinstance(result, list):
                    # Determine if it's a list of LLM references or regex references
                    if len(result) > 0 and isinstance(result[0], dict):
                        llm_references = result
                    else:
                        regex_references = result

                # Add regex references
                for ref in regex_references:
                    all_references.append({
                        "source_document": source_document,
                        "extraction_method": "regex",
                        "reference_text": ref,
                        "authors": "",
                        "title": "",
                        "year": "",
                        "journal": "",
                        "volume": "",
                        "issue": "",
                        "pages": "",
                        "doi": "",
                        "pmid": "",
                        "url": ""
                    })

                # Add LLM references
                for ref in llm_references:
                    # Convert authors list to string if needed
                    authors = ref.get("authors", "")
                    if isinstance(authors, list):
                        authors = "; ".join(authors)

                    all_references.append({
                        "source_document": source_document,
                        "extraction_method": "llm",
                        "reference_text": ref.get("reference_text", ""),  # Include the original reference text
                        "authors": authors,
                        "title": ref.get("title", ""),
                        "year": ref.get("year", ""),
                        "journal": ref.get("journal", ref.get("conference", ref.get("book", ""))),
                        "volume": ref.get("volume", ""),
                        "issue": ref.get("issue", ""),
                        "pages": ref.get("pages", ""),
                        "doi": ref.get("doi", ""),
                        "pmid": ref.get("pmid", ""),
                        "url": ref.get("url", "")
                    })

                # Write to CSV
                if all_references:
                    with open(output_path, 'w', encoding='utf-8', newline='') as f:
                        writer = csv.DictWriter(f, fieldnames=all_references[0].keys())
                        writer.writeheader()
                        writer.writerows(all_references)

                    logger.info(f"Saved references to CSV: {output_path}")
                    return output_path
                else:
                    logger.warning("No references to save to CSV")
                    return None

        except Exception as e:
            logger.error(f"Error saving references to CSV: {e}")
            return None

async def process_directory(directory_path: str, output_dir: str = None, llm_provider: str = 'openai') -> List[Dict[str, Any]]:
    """
    Process all PDF files in a directory

    Args:
        directory_path: Path to the directory containing PDF files
        output_dir: Directory to save reference files (if None, will use the same directory)
        llm_provider: The LLM provider to use ('openai', 'mistral', or None)

    Returns:
        List of result dictionaries
    """
    if not os.path.exists(directory_path):
        logger.error(f"Directory not found: {directory_path}")
        return []

    if not output_dir:
        output_dir = directory_path

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Initialize extractor
    extractor = ReferenceExtractor(llm_provider=llm_provider)

    # Get all PDF files
    pdf_files = [f for f in os.listdir(directory_path) if f.lower().endswith('.pdf')]
    logger.info(f"Found {len(pdf_files)} PDF files in {directory_path}")

    # Process each file
    all_results = []
    for pdf_file in pdf_files:
        pdf_path = os.path.join(directory_path, pdf_file)
        result = await extractor.process_document(pdf_path)
        all_results.append(result)

        # Save individual results
        json_path = os.path.join(output_dir, f"{os.path.splitext(pdf_file)[0]}_references.json")
        csv_path = os.path.join(output_dir, f"{os.path.splitext(pdf_file)[0]}_references.csv")
        extractor.save_references_to_json(result, json_path)
        extractor.save_references_to_csv(result, csv_path)

    # Save combined references to CSV
    try:
        combined_csv_path = os.path.join(output_dir, "all_references.csv")

        # Collect all references
        all_references = []
        for result in all_results:
            filename = result.get("filename", "")

            # Add regex references
            for ref in result.get("regex_references", []):
                all_references.append({
                    "source_document": filename,
                    "extraction_method": "regex",
                    "reference_text": ref,
                    "authors": "",
                    "title": "",
                    "year": "",
                    "journal": "",
                    "volume": "",
                    "issue": "",
                    "pages": "",
                    "doi": "",
                    "pmid": "",
                    "url": ""
                })

            # Add LLM references
            for ref in result.get("llm_references", []):
                # Convert authors list to string if needed
                authors = ref.get("authors", "")
                if isinstance(authors, list):
                    authors = "; ".join(authors)

                all_references.append({
                    "source_document": filename,
                    "extraction_method": "llm",
                    "reference_text": ref.get("reference_text", ""),  # Include the original reference text
                    "authors": authors,
                    "title": ref.get("title", ""),
                    "year": ref.get("year", ""),
                    "journal": ref.get("journal", ref.get("conference", ref.get("book", ""))),
                    "volume": ref.get("volume", ""),
                    "issue": ref.get("issue", ""),
                    "pages": ref.get("pages", ""),
                    "doi": ref.get("doi", ""),
                    "pmid": ref.get("pmid", ""),
                    "url": ref.get("url", "")
                })

        # Write to CSV
        if all_references:
            with open(combined_csv_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=all_references[0].keys())
                writer.writeheader()
                writer.writerows(all_references)

            logger.info(f"Saved combined references to CSV: {combined_csv_path}")

    except Exception as e:
        logger.error(f"Error saving combined references to CSV: {e}")

    return all_results

# Main function for testing
async def main():
    """Test the reference extraction functionality"""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python reference_extraction.py <pdf_file_or_directory>")
        return

    path = sys.argv[1]

    if os.path.isdir(path):
        # Process directory
        await process_directory(path)
    elif os.path.isfile(path) and path.lower().endswith('.pdf'):
        # Process single file
        extractor = ReferenceExtractor()
        result = await extractor.process_document(path)
        extractor.save_references_to_json(result)
        extractor.save_references_to_csv(result)
        print(json.dumps(result, indent=2))
    else:
        print(f"Invalid path or not a PDF file: {path}")

if __name__ == "__main__":
    asyncio.run(main())

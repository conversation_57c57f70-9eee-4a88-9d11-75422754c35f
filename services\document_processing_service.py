"""
Unified document processing service for the Graphiti application.

This service provides a complete workflow for processing documents:
1. Extract text from documents
2. Chunk the text
3. Store the chunks in the database
4. Extract entities from the chunks
5. Deduplicate entities
6. Extract references from the document
7. Generate embeddings for the chunks
"""

import os
import asyncio
import logging
import uuid
import json
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import concurrent.futures

from pdf_processor import extract_text_from_pdf, process_pdf
from entity_extraction import extract_entities_from_text
from reference_extractor import EnhancedReferenceExtractor
from mistral_ocr import MistralOCRProcessor
from database.database_service import get_falkordb_adapter
from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.config import get_config
from utils.logging_utils import get_logger
from utils.file_utils import is_supported_file, get_file_type, get_file_category
from entity_deduplication import EntityDeduplicator
from utils.progress_tracker import ProgressTracker

# Import OpenAI for embeddings (legacy support)
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("OpenAI package not available. Will use alternative embedding methods.")

# Import Ollama client for local embeddings
try:
    from utils.ollama_client import get_ollama_client
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Ollama client not available. Will try other embedding methods.")

# Set up logger
logger = get_logger(__name__)

class DocumentProcessingService:
    """Service for processing documents in a unified workflow."""

    def __init__(self):
        """Initialize the document processing service."""
        self.config = get_config()
        self.reference_extractor = EnhancedReferenceExtractor(llm_provider=self.config['llm']['provider'])

        # Initialize Mistral OCR for enhanced reference extraction
        mistral_api_key = os.getenv('MISTRAL_API_KEY')
        logger.info(f"Mistral API key found: {'Yes' if mistral_api_key else 'No'}")
        if mistral_api_key:
            try:
                self.mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
                logger.info("✅ Successfully initialized Mistral OCR for enhanced reference extraction")
            except Exception as e:
                logger.error(f"❌ Could not initialize Mistral OCR: {e}", exc_info=True)
                self.mistral_ocr = None
        else:
            logger.warning("❌ No Mistral API key found, Mistral OCR will not be available for reference extraction")
            self.mistral_ocr = None
        self.entity_deduplicator = None  # Will be initialized lazily
        self.processing_queue = asyncio.Queue()
        self.is_processing = False
        self.processed_count = 0
        self.failed_count = 0
        self.current_document = None
        self.max_parallel_processes = 4
        self.active_processes = 0
        self.semaphore = asyncio.Semaphore(self.max_parallel_processes)
        self.document_progress = {}
        self.current_document_progress = {
            "document_id": None,
            "filename": None,
            "total_steps": 6,  # Added deduplication step
            "current_step": 0,
            "step_name": "No document processing",
            "progress_percentage": 0,
            "status": "idle",
            "details": {}
        }

        # Create directories if they don't exist
        os.makedirs(self.config['paths']['processed_dir'], exist_ok=True)
        os.makedirs(self.config['paths']['references_dir'], exist_ok=True)

    async def get_entity_deduplicator(self):
        """
        Get the entity deduplicator, initializing it if necessary.

        Returns:
            EntityDeduplicator instance
        """
        if not self.entity_deduplicator:
            self.entity_deduplicator = EntityDeduplicator()
        return self.entity_deduplicator

    async def process_document(
        self,
        file_path: Union[str, Path],
        chunk_size: int = 1200,
        overlap: int = 0,
        extract_entities: bool = True,
        extract_references: bool = True,
        extract_metadata: bool = True,
        generate_embeddings: bool = True
    ) -> Dict[str, Any]:
        """
        Process a document through the complete workflow.

        Args:
            file_path: Path to the document file
            chunk_size: Size of text chunks in characters
            overlap: Overlap between chunks in characters
            extract_entities: Whether to extract entities
            extract_references: Whether to extract references
            extract_metadata: Whether to extract metadata
            generate_embeddings: Whether to generate embeddings for facts

        Returns:
            Result dictionary with processing details
        """
        file_path = Path(file_path)
        logger.info(f"Processing document: {file_path}")

        # Initialize enhanced progress tracking
        document_id = str(file_path.stem)
        filename = file_path.name
        progress_tracker = ProgressTracker(document_id, filename)

        # Update current document progress with the tracker data
        self.current_document_progress = progress_tracker.get_progress_data()

        try:
            # Step 1: Process the document and add it to the knowledge graph
            progress_tracker.update_progress(
                step=1,
                step_name="Extracting text from document",
                progress_percentage=10,
                status="processing"
            )
            self.current_document_progress = progress_tracker.get_progress_data()

            pdf_result = await process_pdf(str(file_path), chunk_size, overlap)

            if not pdf_result.get("success", False):
                logger.error(f"Failed to process document: {file_path}")
                progress_tracker.fail(pdf_result.get("error", "Unknown error"))
                self.current_document_progress = progress_tracker.get_progress_data()
                return pdf_result

            episode_id = pdf_result["episode_id"]
            progress_tracker.update_progress(
                step=1,
                step_name="Extracting text from document",
                progress_percentage=20,
                status="processing",
                details={"chunks": pdf_result.get("chunks", 0)}
            )
            self.current_document_progress = progress_tracker.get_progress_data()
            logger.info(f"Document processed successfully. Episode ID: {episode_id}")

            # Step 2: Extract entities if requested
            entities_result = {"entities_extracted": 0}
            if extract_entities:
                progress_tracker.update_progress(
                    step=2,
                    step_name="Extracting entities",
                    progress_percentage=30,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                entities_result = await self.extract_entities_from_document(episode_id)
                entities_count = entities_result.get("entities_extracted", 0)
                progress_tracker.update_entities_count(entities_count)
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Extracted {entities_count} entities from document")

            # Step 3: Deduplicate entities if entities were extracted
            deduplication_result = {"duplicates_found": 0, "entities_merged": 0}
            if extract_entities and entities_result.get("entities_extracted", 0) > 0:
                progress_tracker.update_progress(
                    step=3,
                    step_name="Deduplicating entities",
                    progress_percentage=45,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                deduplication_result = await self.deduplicate_entities_for_document(episode_id)
                duplicates_found = deduplication_result.get("duplicates_found", 0)
                entities_merged = deduplication_result.get("entities_merged", 0)

                progress_tracker.update_progress(
                    step=3,
                    step_name="Deduplicating entities",
                    progress_percentage=55,
                    status="processing",
                    details={
                        "duplicates_found": duplicates_found,
                        "entities_merged": entities_merged
                    }
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Found {duplicates_found} duplicate entities and merged {entities_merged} entities")

            # Step 4: Extract references if requested
            references_result = {"references_extracted": 0, "total_reference_count": 0}
            if extract_references:
                progress_tracker.update_progress(
                    step=4,
                    step_name="Extracting references",
                    progress_percentage=60,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                references_result = await self.extract_references_from_document(str(file_path))
                references_count = references_result.get("total_reference_count", 0)
                progress_tracker.update_references_count(references_count)
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Extracted {references_count} references from document")

            # Step 5: Extract metadata if requested
            metadata_result = {"metadata_extracted": False}
            if extract_metadata:
                progress_tracker.update_progress(
                    step=5,
                    step_name="Extracting metadata",
                    progress_percentage=75,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                metadata_result = await self.extract_metadata_from_document(str(file_path), episode_id)
                metadata_extracted = metadata_result.get("metadata_extracted", False)

                progress_tracker.update_progress(
                    step=5,
                    step_name="Extracting metadata",
                    progress_percentage=85,
                    status="processing",
                    details={"metadata_extracted": metadata_extracted}
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Metadata extraction {'successful' if metadata_extracted else 'failed'}")

            # Step 6: Generate embeddings if requested
            embeddings_result = {"embeddings_generated": 0}
            if generate_embeddings:
                progress_tracker.update_progress(
                    step=5,
                    step_name="Generating embeddings",
                    progress_percentage=90,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                embeddings_result = await self.generate_embeddings_for_document(episode_id)
                embeddings_count = embeddings_result.get("embeddings_generated", 0)
                embedding_model = embeddings_result.get("embedding_model", "none")

                progress_tracker.update_embeddings_count(embeddings_count)
                progress_tracker.update_progress(
                    step=5,
                    step_name="Generating embeddings",
                    progress_percentage=95,
                    status="processing",
                    details={"embedding_model": embedding_model}
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Generated {embeddings_count} embeddings for document using model {embedding_model}")

            # Combine results
            result = {
                **pdf_result,
                "entities_extracted": entities_result.get("entities_extracted", 0),
                "duplicates_found": deduplication_result.get("duplicates_found", 0),
                "entities_merged": deduplication_result.get("entities_merged", 0),
                "references_extracted": references_result.get("total_reference_count", 0),
                "metadata_extracted": metadata_result.get("metadata_extracted", False),
                "embeddings_generated": embeddings_result.get("embeddings_generated", 0),
                "embedding_model": embeddings_result.get("embedding_model", "none")
            }

            # Update final progress
            progress_tracker.complete(details=result)
            self.current_document_progress = progress_tracker.get_progress_data()

            return result

        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}", exc_info=True)
            progress_tracker.fail(str(e))
            self.current_document_progress = progress_tracker.get_progress_data()
            return {
                "success": False,
                "error": str(e),
                "file_path": str(file_path)
            }

    async def deduplicate_entities_for_document(self, episode_id: str) -> Dict[str, Any]:
        """
        Deduplicate entities for a document.

        Args:
            episode_id: ID of the episode node

        Returns:
            Result dictionary with deduplication details
        """
        logger.info(f"Deduplicating entities for document with episode ID: {episode_id}")

        try:
            # Get the entity deduplicator
            deduplicator = await self.get_entity_deduplicator()

            # Run deduplication for entities from this document
            result = await deduplicator.deduplicate_entities(
                document_id=episode_id,
                merge=True
            )

            logger.info(f"Deduplication completed for document {episode_id}")
            logger.info(f"Found {result.duplicate_groups} duplicate groups with {result.total_duplicates} total duplicates")
            logger.info(f"Merged {result.merged_entities} entities")

            return {
                "success": True,
                "document_id": episode_id,
                "duplicates_found": result.total_duplicates,
                "duplicate_groups": result.duplicate_groups,
                "entities_merged": result.merged_entities,
                "processing_time": result.processing_time
            }

        except Exception as e:
            logger.error(f"Error deduplicating entities for document {episode_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "document_id": episode_id,
                "duplicates_found": 0,
                "entities_merged": 0
            }

    async def extract_entities_from_document(self, episode_id: str) -> Dict[str, Any]:
        """
        Extract entities from a document's facts.

        Args:
            episode_id: ID of the episode node

        Returns:
            Result dictionary with entity extraction details
        """
        logger.info(f"Extracting entities from document with episode ID: {episode_id}")

        try:
            # Get the adapter
            adapter = await get_falkordb_adapter()

            # Get facts for this episode
            query = f"""
            MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
            RETURN f.uuid AS uuid, f.body AS body
            """

            result = adapter.execute_cypher(query)

            if not result or len(result) < 2 or not result[1]:
                logger.warning(f"No facts found for episode {episode_id}")
                return {"success": False, "error": "No facts found", "entities_extracted": 0}

            logger.info(f"Found {len(result[1])} facts for episode {episode_id}")

            # Extract entities from each fact
            entities_extracted = 0

            # Get the OpenAI API key from environment variables
            from utils.config import OPENAI_API_KEY
            api_key = OPENAI_API_KEY

            # Get the OpenRouter API key from environment variables
            from utils.config import OPENROUTER_API_KEY
            openrouter_api_key = OPENROUTER_API_KEY

            # Use OpenRouter API key if available
            if openrouter_api_key:
                api_key = openrouter_api_key
                logger.info("Using OpenRouter API key for entity extraction")
            elif api_key:
                logger.info("Using OpenAI API key for entity extraction")
            else:
                logger.warning("No API key found for entity extraction")
                return {"success": False, "error": "No API key found for entity extraction", "entities_extracted": 0}

            for row in result[1]:
                fact_uuid = row[0]
                fact_body = row[1]

                logger.info(f"Processing fact: {fact_uuid}")
                logger.info(f"Fact body sample: {fact_body[:100]}...")

                # Extract entities from the fact text
                entities = await extract_entities_from_text(api_key, fact_body)

                logger.info(f"Extracted {len(entities)} entities from fact {fact_uuid}")

                # Create entity nodes and relationships
                for entity in entities:
                    logger.info(f"- {entity['name']} ({entity['type']}): {entity.get('description', '')}")

                    # Escape special characters in entity name and description
                    entity_name = entity["name"].replace("'", "\\'")
                    entity_type = entity["type"]
                    entity_description = entity.get("description", "").replace("'", "\\'")

                    # Get current timestamp
                    timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")

                    # Create entity node with UUID and link to fact
                    entity_uuid = str(uuid.uuid4())
                    create_entity_query = f"""
                    MERGE (e:Entity {{name: '{entity_name}', type: '{entity_type}'}})
                    ON CREATE SET
                        e.uuid = '{entity_uuid}',
                        e.description = '{entity_description}',
                        e.created_at = '{timestamp}',
                        e.source_document_id = '{episode_id}',
                        e.source_fact_id = '{fact_uuid}',
                        e.confidence = 1.0
                    ON MATCH SET
                        e.uuid = CASE WHEN e.uuid IS NULL THEN '{entity_uuid}' ELSE e.uuid END
                    WITH e
                    MATCH (f:Fact {{uuid: '{fact_uuid}'}})
                    MERGE (f)-[r:MENTIONS]->(e)
                    RETURN e.name as name, e.uuid as uuid
                    """

                    result = adapter.execute_cypher(create_entity_query)
                    if result and len(result) > 1 and len(result[1]) > 0:
                        logger.info(f"  Created entity node and relationship for {entity['name']}")
                        entities_extracted += 1
                    else:
                        logger.error(f"  Failed to create entity node for {entity['name']}")

            logger.info(f"Extracted and created {entities_extracted} entities for episode {episode_id}")

            return {
                "success": True,
                "document_id": episode_id,
                "entities_extracted": entities_extracted
            }

        except Exception as e:
            logger.error(f"Error extracting entities from document {episode_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "document_id": episode_id,
                "entities_extracted": 0
            }

    async def extract_references_from_document(self, file_path: str) -> Dict[str, Any]:
        """
        Extract references from a document using Mistral OCR as the primary method.

        Args:
            file_path: Path to the document file

        Returns:
            Result dictionary with reference extraction details
        """
        logger.info(f"Extracting references from document: {file_path}")

        try:
            # Use Mistral OCR as the primary reference extraction method
            logger.info(f"🔍 Checking Mistral OCR availability: {'Available' if self.mistral_ocr else 'Not Available'}")
            if self.mistral_ocr:
                logger.info("🚀 Starting Mistral OCR reference extraction...")
                result = await self._extract_references_with_mistral_ocr(file_path)
                if result.get("success", False) and result.get("total_reference_count", 0) > 0:
                    logger.info(f"✅ Successfully extracted {result.get('total_reference_count', 0)} references using Mistral OCR")
                    return result
                else:
                    logger.warning(f"⚠️ Mistral OCR returned no references (success: {result.get('success', False)}, count: {result.get('total_reference_count', 0)}), falling back to standard extractor")
            else:
                logger.warning("⚠️ Mistral OCR not available, using standard reference extractor")

            # Fallback to standard reference extractor
            result = await self.reference_extractor.process_document(file_path)

            if not result.get("success", False):
                logger.error(f"Failed to extract references from {file_path}")
                return result

            # Save references to CSV and JSON
            filename = os.path.basename(file_path)
            base_filename = os.path.splitext(filename)[0]

            # Save to references directory
            csv_path = os.path.join(self.config['paths']['references_dir'], f"{base_filename}_references.csv")
            json_path = os.path.join(self.config['paths']['references_dir'], f"{base_filename}_references.json")

            self.reference_extractor.save_references_to_csv(result, csv_path)
            self.reference_extractor.save_references_to_json(result, json_path)

            return result

        except Exception as e:
            logger.error(f"Error extracting references from document {file_path}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path,
                "total_reference_count": 0
            }

    async def _extract_references_with_mistral_ocr(self, file_path: str) -> Dict[str, Any]:
        """
        Extract references using Mistral OCR with specialized prompts for reference extraction.

        Args:
            file_path: Path to the PDF document

        Returns:
            Result dictionary with reference extraction details
        """
        try:
            logger.info(f"Using Mistral OCR for reference extraction from {file_path}")

            # Create a specialized prompt for reference extraction
            reference_extraction_prompt = """
            You are a specialized reference extraction system designed to identify and extract ALL bibliographic references from academic and scientific documents.

            Your primary task is to extract references in a structured format. Focus on:

            1. **Reference Sections**: Look for dedicated sections like "References", "Bibliography", "Sources", "Citations", "Literature Cited", etc.

            2. **Reference Formats**: Extract references in any format including:
               - Numbered references: [1], (1), 1.
               - Author-year citations: (Smith et al., 2020)
               - Full bibliographic entries with authors, titles, journals, years, volumes, pages
               - References with DOIs: doi: 10.1234/example
               - References with PMIDs: PMID: 12345678
               - URL references and web citations
               - Book citations and conference proceedings

            3. **Non-standard Locations**: References may appear in:
               - Slide presentations (e.g., "References for Previous Slide")
               - Footnotes and endnotes
               - Figure captions and table notes
               - Appendices and supplementary materials
               - Headers and footers

            4. **Incomplete References**: Some references may be cut off or incomplete. Extract what you can and note if incomplete.

            For each reference found, extract the following information when available:
            - Authors (full names or last name, first initial)
            - Title of the work
            - Journal/Conference/Book name
            - Publication year
            - Volume and issue numbers
            - Page numbers
            - DOI or PMID
            - URL if applicable

            Return the references as a JSON array where each reference is an object with the extracted fields.
            Include a "raw_text" field with the original reference text as it appears in the document.
            Include an "extraction_method" field set to "mistral_ocr".
            Include a "confidence" field (high/medium/low) based on how complete the reference appears.

            Example output format:
            [
                {
                    "authors": "Smith, J., Johnson, A., et al.",
                    "title": "A comprehensive study of...",
                    "journal": "Nature Medicine",
                    "year": "2020",
                    "volume": "26",
                    "issue": "3",
                    "pages": "123-130",
                    "doi": "10.1038/s41591-020-0820-9",
                    "pmid": "32161990",
                    "raw_text": "Smith, J., Johnson, A., et al. A comprehensive study of... Nature Medicine. 2020; 26(3): 123-130. doi: 10.1038/s41591-020-0820-9. PMID: 32161990",
                    "extraction_method": "mistral_ocr",
                    "confidence": "high"
                }
            ]

            Extract ALL references you can find, even if they appear in unusual locations or formats.
            """

            # Use Mistral OCR to extract text with reference-focused processing
            extracted_text = await self.mistral_ocr.extract_text_from_pdf(file_path)

            if not extracted_text:
                logger.warning("Mistral OCR returned no text")
                return {
                    "success": False,
                    "error": "No text extracted by Mistral OCR",
                    "file_path": file_path,
                    "total_reference_count": 0
                }

            # Now use Mistral's chat completion to extract structured references
            from mistralai import Mistral
            mistral_client = Mistral(api_key=os.getenv('MISTRAL_API_KEY'))

            # Create messages for reference extraction
            messages = [
                {
                    "role": "system",
                    "content": reference_extraction_prompt
                },
                {
                    "role": "user",
                    "content": f"Please extract all references from the following document text:\n\n{extracted_text}"
                }
            ]

            # Call Mistral for reference extraction
            response = mistral_client.chat.complete(
                model="mistral-large-latest",
                messages=messages,
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=4000
            )

            # Parse the response
            response_text = response.choices[0].message.content

            try:
                # Try to parse as JSON
                import json
                import time
                references = json.loads(response_text)

                # Validate that it's a list
                if not isinstance(references, list):
                    logger.warning("Mistral OCR response is not a list, attempting to extract from text")
                    return {
                        "success": False,
                        "error": "Invalid response format from Mistral OCR",
                        "file_path": file_path,
                        "total_reference_count": 0
                    }

                # Add metadata to each reference
                for ref in references:
                    if isinstance(ref, dict):
                        ref["extraction_timestamp"] = int(time.time())
                        ref["source_file"] = os.path.basename(file_path)
                        if "extraction_method" not in ref:
                            ref["extraction_method"] = "mistral_ocr"

                logger.info(f"Successfully extracted {len(references)} references using Mistral OCR")

                # Return in the expected format
                return {
                    "success": True,
                    "file_path": file_path,
                    "extraction_date": datetime.now(timezone.utc).isoformat(),
                    "total_reference_count": len(references),
                    "llm_references": references,
                    "regex_references": [],  # Mistral OCR handles all extraction
                    "extraction_method": "mistral_ocr"
                }

            except json.JSONDecodeError as e:
                logger.error(f"Could not parse Mistral OCR response as JSON: {e}")
                logger.debug(f"Response text: {response_text[:500]}...")
                return {
                    "success": False,
                    "error": f"JSON parsing error: {str(e)}",
                    "file_path": file_path,
                    "total_reference_count": 0
                }

        except Exception as e:
            logger.error(f"Error in Mistral OCR reference extraction: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path,
                "total_reference_count": 0
            }

    async def extract_metadata_from_document(self, file_path: str, episode_id: str) -> Dict[str, Any]:
        """
        Extract metadata from a document and update the episode node.

        Args:
            file_path: Path to the document file
            episode_id: ID of the episode node

        Returns:
            Result dictionary with metadata extraction details
        """
        logger.info(f"Extracting metadata from document: {file_path}")

        try:
            # For now, we'll just extract basic metadata from the filename
            # In a future implementation, this could use LLM to extract more detailed metadata
            filename = os.path.basename(file_path)
            base_filename = os.path.splitext(filename)[0]

            # Update the episode node with metadata
            adapter = await get_falkordb_adapter()

            query = f"""
            MATCH (e:Episode {{uuid: '{episode_id}'}})
            SET e.title = '{base_filename}',
                e.file_type = '{get_file_type(filename)}',
                e.file_category = '{get_file_category(filename)}',
                e.metadata_extracted = true,
                e.metadata_extraction_date = '{datetime.now(timezone.utc).isoformat()}'
            RETURN e.uuid
            """

            result = adapter.execute_cypher(query)

            if result and len(result) > 1 and len(result[1]) > 0:
                return {
                    "success": True,
                    "document_id": episode_id,
                    "metadata_extracted": True
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to update episode with metadata",
                    "document_id": episode_id,
                    "metadata_extracted": False
                }

        except Exception as e:
            logger.error(f"Error extracting metadata from document {file_path}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "document_id": episode_id,
                "metadata_extracted": False
            }

    async def generate_embeddings_for_document(self, episode_id: str) -> Dict[str, Any]:
        """
        Generate embeddings for all facts in a document.

        Args:
            episode_id: ID of the episode node

        Returns:
            Result dictionary with embedding generation details
        """
        logger.info(f"Generating embeddings for document with episode ID: {episode_id}")

        try:
            # Get the adapter
            adapter = await get_falkordb_adapter()

            # Create vector index if it doesn't exist
            await self._create_vector_index(adapter)

            # Get facts for this episode
            query = f"""
            MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
            RETURN f.uuid AS uuid, f.body AS body
            """

            result = adapter.execute_cypher(query)

            if not result or len(result) < 2 or not result[1]:
                logger.warning(f"No facts found for episode {episode_id}")
                return {
                    "success": False,
                    "error": "No facts found",
                    "document_id": episode_id,
                    "embeddings_generated": 0
                }

            facts = []
            for row in result[1]:
                facts.append({
                    "uuid": row[0],
                    "body": row[1]
                })

            logger.info(f"Found {len(facts)} facts for episode {episode_id}")

            # Get OpenAI API key as fallback
            api_key = None
            if OPENAI_AVAILABLE:
                try:
                    from utils.config import OPENAI_API_KEY
                    api_key = OPENAI_API_KEY

                    if not api_key:
                        # Try to get from environment variables as fallback
                        api_key = os.environ.get('OPENAI_API_KEY')
                except Exception:
                    logger.warning("Could not retrieve OpenAI API key, will use local embeddings only")

            # Generate embeddings in batches
            batch_size = 20
            embeddings_generated = 0
            embedding_model_used = "unknown"

            for i in range(0, len(facts), batch_size):
                batch = facts[i:i+batch_size]
                texts = [fact["body"] for fact in batch]

                # Generate embeddings using available methods
                embeddings = await self._generate_embeddings(api_key, texts)

                # Determine which model was used based on the embedding dimensions
                if embeddings and len(embeddings) > 0:
                    dimensions = len(embeddings[0])
                    if dimensions == 1024:
                        embedding_model_used = "snowflake-arctic-embed2"
                    elif dimensions == 1536:
                        embedding_model_used = "text-embedding-3-small"
                    else:
                        embedding_model_used = f"unknown-{dimensions}d"

                # Store embeddings in Redis and metadata in FalkorDB
                for j, fact in enumerate(batch):
                    if j < len(embeddings):  # Make sure we have an embedding for this fact
                        # Generate a unique embedding ID
                        embedding_id = f"{fact['uuid']}_embedding"
                        timestamp = datetime.now(timezone.utc).isoformat()

                        # Store embedding in Redis Vector Search
                        from utils.redis_vector_search import store_embedding
                        store_success = store_embedding(
                            fact_uuid=fact["uuid"],
                            episode_uuid=episode_id,
                            body=fact["body"],
                            embedding=embeddings[j],
                            embedding_id=embedding_id
                        )

                        if not store_success:
                            logger.warning(f"Failed to store embedding in Redis for fact {fact['uuid']}")
                            continue

                        # Store only metadata in FalkorDB
                        query = f"""
                        MATCH (f:Fact {{uuid: '{fact["uuid"]}'}})
                        SET f.embedding_id = '{embedding_id}',
                            f.embedding_model = '{embedding_model_used}',
                            f.embedding_dimensions = {len(embeddings[j])},
                            f.embedding_updated_at = '{timestamp}',
                            f.embedding_in_redis = true
                        RETURN f.uuid
                        """

                        result = adapter.execute_cypher(query)
                        if result and len(result) > 1 and len(result[1]) > 0:
                            embeddings_generated += 1
                        else:
                            logger.warning(f"Failed to update fact {fact['uuid']} with embedding metadata")

                logger.info(f"Generated embeddings for {len(batch)} facts (batch {i//batch_size + 1}/{(len(facts)-1)//batch_size + 1})")

            logger.info(f"Generated embeddings for {embeddings_generated} facts in episode {episode_id} using model {embedding_model_used}")

            # Sync embeddings to Redis Vector Search
            try:
                from services.embedding_sync_service import get_embedding_sync_service

                # Get embedding sync service
                sync_service = await get_embedding_sync_service()

                # Sync embeddings for this episode
                sync_result = await sync_service.sync_embeddings_for_episode(episode_id)

                if sync_result["success"]:
                    logger.info(f"Synced {sync_result['facts_synced']} embeddings to Redis Vector Search")
                else:
                    logger.warning(f"Failed to sync embeddings to Redis Vector Search: {sync_result.get('error', 'Unknown error')}")
            except Exception as e:
                logger.error(f"Error syncing embeddings to Redis Vector Search: {e}")

            return {
                "success": True,
                "document_id": episode_id,
                "embeddings_generated": embeddings_generated,
                "embedding_model": embedding_model_used,
                "embeddings_synced_to_redis": True
            }

        except Exception as e:
            logger.error(f"Error generating embeddings for document {episode_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "document_id": episode_id,
                "embeddings_generated": 0
            }

    async def _generate_embeddings(self, api_key: str = None, texts: List[str] = None, model: str = None) -> List[List[float]]:
        """
        Generate embeddings for a list of texts using available providers.

        This function tries different embedding providers in the following order:
        1. Ollama (local) with Snowflake Arctic Embed2 model
        2. OpenAI (if API key is provided)

        Args:
            api_key: OpenAI API key (optional)
            texts: List of texts to embed
            model: Embedding model to use (optional, provider-specific)

        Returns:
            List of embedding vectors
        """
        if not texts:
            logger.warning("No texts provided for embedding generation")
            return []

        logger.info(f"Generating embeddings for {len(texts)} texts")

        # Try Ollama first (local, no API key needed)
        if OLLAMA_AVAILABLE:
            try:
                logger.info("Attempting to generate embeddings using Ollama with Snowflake Arctic Embed2 model")
                ollama_client = get_ollama_client()

                # Check if Ollama is available
                if ollama_client.is_available():
                    # Check if the Snowflake model is available
                    if ollama_client.is_model_available("snowflake-arctic-embed2"):
                        embeddings = ollama_client.generate_embeddings(texts, model="snowflake-arctic-embed2")
                        logger.info(f"Successfully generated {len(embeddings)} embeddings using Ollama with Snowflake Arctic Embed2")
                        return embeddings
                    else:
                        logger.warning("Snowflake Arctic Embed2 model not available in Ollama, trying other models")

                        # Try other embedding models in Ollama
                        for fallback_model in ["bge-m3", "nomic-embed-text", "mistral"]:
                            if ollama_client.is_model_available(fallback_model):
                                logger.info(f"Using fallback Ollama model: {fallback_model}")
                                embeddings = ollama_client.generate_embeddings(texts, model=fallback_model)
                                logger.info(f"Successfully generated {len(embeddings)} embeddings using Ollama with {fallback_model}")
                                return embeddings
                else:
                    logger.warning("Ollama service not available")
            except Exception as e:
                logger.error(f"Error generating embeddings with Ollama: {e}")
        else:
            logger.warning("Ollama client not available")

        # Fall back to OpenAI if available and API key is provided
        if OPENAI_AVAILABLE and api_key:
            try:
                logger.info(f"Falling back to OpenAI for embeddings using model: {model or 'text-embedding-3-small'}")
                openai_model = model or "text-embedding-3-small"

                # Initialize OpenAI client
                try:
                    client = OpenAI(api_key=api_key)
                    response = client.embeddings.create(
                        input=texts,
                        model=openai_model
                    )

                    embeddings = [data.embedding for data in response.data]
                    logger.info(f"Successfully generated {len(embeddings)} embeddings using OpenAI")
                    return embeddings
                except Exception as e:
                    logger.error(f"Error with OpenAI client: {e}")
                    raise
            except Exception as e:
                logger.error(f"Error generating embeddings with OpenAI: {e}")

        # If all methods fail, return empty embeddings
        logger.error("All embedding generation methods failed")
        return []

    async def _create_vector_index(self, adapter) -> bool:
        """
        Create a vector index in FalkorDB for Fact nodes.

        Args:
            adapter: FalkorDB adapter

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Creating vector index for Fact nodes")

        # FalkorDB doesn't support vector indexes yet, but we'll keep this method
        # for future compatibility. For now, we'll just return True.
        logger.info("Vector indexes not supported in current FalkorDB version. Skipping index creation.")
        return True

    async def add_document_to_queue(self, file_path: Union[str, Path]) -> bool:
        """
        Add a document to the processing queue.

        Args:
            file_path: Path to the document file

        Returns:
            True if the document was added to the queue, False otherwise
        """
        file_path = Path(file_path)

        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return False

        if not is_supported_file(file_path.name):
            logger.error(f"Unsupported file type: {file_path}")
            return False

        await self.processing_queue.put(file_path)
        logger.info(f"Added document to processing queue: {file_path}")

        # Start the processing loop if it's not already running
        if not self.is_processing:
            asyncio.create_task(self.process_queue())

        return True

    async def process_queue(self):
        """Process documents in the queue."""
        if self.is_processing:
            return

        self.is_processing = True
        logger.info("Started processing queue")

        try:
            # Create a list to hold all processing tasks
            tasks = []

            # Process documents in parallel up to max_parallel_processes
            while not self.processing_queue.empty():
                file_path = await self.processing_queue.get()

                # Create a task for processing this document
                task = asyncio.create_task(self.process_document_with_semaphore(file_path))
                tasks.append(task)

                # Don't wait for the task to complete - it will be processed in parallel

            # Wait for all tasks to complete
            if tasks:
                await asyncio.gather(*tasks)

        except Exception as e:
            logger.error(f"Error processing queue: {e}", exc_info=True)

        finally:
            self.is_processing = False
            logger.info("Finished processing queue")

    async def process_document_with_semaphore(self, file_path):
        """
        Process a document with semaphore to limit parallel processing.

        Args:
            file_path: Path to the document file

        Returns:
            Processing result
        """
        async with self.semaphore:
            self.active_processes += 1
            logger.info(f"Processing document from queue: {file_path} (Active processes: {self.active_processes}/{self.max_parallel_processes})")

            try:
                # Store the current document being processed
                document_id = str(Path(file_path).stem)
                self.document_progress[document_id] = {
                    "document_id": document_id,
                    "filename": Path(file_path).name,
                    "total_steps": 5,
                    "current_step": 0,
                    "step_name": "Starting document processing",
                    "progress_percentage": 0,
                    "status": "processing",
                    "details": {}
                }

                # Process the document
                result = await self.process_document(file_path)

                # Update statistics
                if result.get("success", False):
                    self.processed_count += 1
                    logger.info(f"Successfully processed document: {file_path}")
                    logger.info(f"Entities extracted: {result.get('entities_extracted', 0)}")
                    logger.info(f"References extracted: {result.get('references_extracted', 0)}")
                    logger.info(f"Embeddings generated: {result.get('embeddings_generated', 0)}")

                    # Update progress
                    self.document_progress[document_id]["status"] = "completed"
                    self.document_progress[document_id]["progress_percentage"] = 100
                    self.document_progress[document_id]["step_name"] = "Processing complete"
                else:
                    self.failed_count += 1
                    logger.error(f"Failed to process document: {file_path}")

                    # Update progress
                    self.document_progress[document_id]["status"] = "failed"
                    self.document_progress[document_id]["details"]["error"] = result.get("error", "Unknown error")

                # Mark the task as done
                self.processing_queue.task_done()

                return result

            except Exception as e:
                logger.error(f"Error processing document {file_path}: {e}", exc_info=True)
                self.failed_count += 1

                # Mark the task as done
                self.processing_queue.task_done()

                return {
                    "success": False,
                    "error": str(e),
                    "file_path": str(file_path)
                }

            finally:
                self.active_processes -= 1

    async def get_queue_status(self) -> Dict[str, Any]:
        """
        Get the status of the processing queue.

        Returns:
            Status dictionary
        """
        return {
            "is_processing": self.is_processing,
            "queue_size": self.processing_queue.qsize(),
            "processed_count": self.processed_count,
            "failed_count": self.failed_count,
            "active_processes": self.active_processes,
            "max_parallel_processes": self.max_parallel_processes,
            "current_document": str(self.current_document) if self.current_document else None,
            "current_progress": self.current_document_progress,
            "documents_progress": self.document_progress
        }

# Create a singleton instance
_document_processing_service = None

async def get_document_processing_service() -> DocumentProcessingService:
    """
    Get the document processing service instance.

    Returns:
        DocumentProcessingService instance
    """
    global _document_processing_service

    if _document_processing_service is None:
        _document_processing_service = DocumentProcessingService()

    return _document_processing_service

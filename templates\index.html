<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti Knowledge Graph Explorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            padding-top: 20px;
        }
        .result-card {
            margin-bottom: 15px;
            border-left: 4px solid #0d6efd;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .score-badge {
            float: right;
            margin-left: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .source-link {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #graph-container {
            background-color: #f8f9fa;
        }
        .vis-network {
            outline: none;
        }
        #dropzone {
            border: 2px dashed #0d6efd;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        #dropzone.dragover {
            background-color: #e3f2fd;
            border-color: #0d6efd;
        }
        .dropzone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .dropzone-content i {
            font-size: 48px;
            color: #0d6efd;
            margin-bottom: 15px;
        }
        .file-item {
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        #answer-sources {
            max-height: 300px;
            overflow-y: auto;
        }
        .reference-area {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin-top: 10px;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
        }
        .metadata-card {
            margin-bottom: 15px;
        }
        .metadata-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metadata-value {
            margin-bottom: 10px;
        }
        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .user-message {
            background-color: #e9ecef;
            margin-left: auto;
            border-top-right-radius: 0;
        }
        .assistant-message {
            background-color: #f0f7ff;
            margin-right: auto;
            border-top-left-radius: 0;
        }
        .message-container {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }
        .message-content {
            word-wrap: break-word;
        }
        .message-content p {
            margin-bottom: 0.5rem;
        }
        .message-content p:last-child {
            margin-bottom: 0;
        }
        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-header {
            text-align: right;
            color: #495057;
        }
        .assistant-header {
            color: #0d6efd;
        }
        .sources-toggle {
            margin-top: 5px;
            text-align: right;
        }
        .sources-container {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        #answer-loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        #answer-sources {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graphiti Knowledge Graph Explorer</h1>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="chat-tab" data-bs-toggle="tab" data-bs-target="#chat" type="button" role="tab" aria-controls="chat" aria-selected="true">Chat</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab" aria-controls="search" aria-selected="false">Search</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="entities-tab" data-bs-toggle="tab" data-bs-target="#entities" type="button" role="tab" aria-controls="entities" aria-selected="false">Entities</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab" aria-controls="upload" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" href="/batch-upload" id="batch-upload-tab"><i class="bi bi-files"></i> Batch Upload <span class="badge bg-primary">New</span></a>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="metadata-tab" data-bs-toggle="tab" data-bs-target="#metadata" type="button" role="tab" aria-controls="metadata" aria-selected="false">Metadata</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="references-tab" data-bs-toggle="tab" data-bs-target="#references" type="button" role="tab" aria-controls="references" aria-selected="false">References</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="enhancements-tab" data-bs-toggle="tab" data-bs-target="#enhancements" type="button" role="tab" aria-controls="enhancements" aria-selected="false">Enhancements</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="graph-tab" data-bs-toggle="tab" data-bs-target="#graph" type="button" role="tab" aria-controls="graph" aria-selected="false">Graph</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">Settings</button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabsContent">
            <!-- Chat Tab -->
            <div class="tab-pane fade show active" id="chat" role="tabpanel" aria-labelledby="chat-tab">
                <div class="row mb-4">
                    <div class="col-md-10">
                        <div class="input-group">
                            <input type="text" id="question-input" class="form-control" placeholder="Ask a question...">
                            <button class="btn btn-primary" type="button" id="question-button">Ask</button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" type="button" id="clear-chat-button">Clear Chat</button>
                    </div>
                </div>

                <div id="conversation-container" class="mb-4" style="max-height: 500px; overflow-y: auto;">
                    <div id="conversation-history"></div>
                </div>

                <div id="answer-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Generating answer...</p>
                </div>

                <div id="answer-sources" style="display: none;">
                    <div id="sources-list"></div>
                </div>
            </div>

            <!-- Search Tab -->
            <div class="tab-pane fade" id="search" role="tabpanel" aria-labelledby="search-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Search</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="input-group">
                                            <input type="text" id="search-input" class="form-control" placeholder="Enter your search query...">
                                            <button class="btn btn-primary" type="button" id="search-button">Search</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="searchType" id="semanticSearch" value="semantic" checked>
                                            <label class="form-check-label" for="semanticSearch">Semantic Search</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="searchType" id="hybridSearch" value="hybrid">
                                            <label class="form-check-label" for="hybridSearch">Hybrid Search</label>
                                        </div>
                                    </div>
                                </div>
                                <div id="search-loading" class="loading" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Searching...</p>
                                </div>
                                <div id="search-results">
                                    <!-- Search results will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Entities Tab -->
            <div class="tab-pane fade" id="entities" role="tabpanel" aria-labelledby="entities-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Entities</h5>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="input-group mb-3 mt-3">
                                        <input type="text" id="entity-search-input" class="form-control" placeholder="Search entities...">
                                        <button class="btn btn-outline-secondary" type="button" id="entity-search-button">Search</button>
                                    </div>
                                    <div class="d-flex gap-3">
                                        <div class="form-group">
                                            <label for="entity-type-filter" class="form-label">Entity Type</label>
                                            <select id="entity-type-filter" class="form-select">
                                                <option value="">All Types</option>
                                                <option value="Application">Application</option>
                                                <option value="Complex">Complex</option>
                                                <option value="Concept">Concept</option>
                                                <option value="Disease">Disease</option>
                                                <option value="Food">Food</option>
                                                <option value="Herb">Herb</option>
                                                <option value="Location">Location</option>
                                                <option value="Medication">Medication</option>
                                                <option value="Nutrient">Nutrient</option>
                                                <option value="Organization">Organization</option>
                                                <option value="Person">Person</option>
                                                <option value="Process">Process</option>
                                                <option value="Research">Research</option>
                                                <option value="Supplement">Supplement</option>
                                                <option value="Symptom">Symptom</option>
                                                <option value="Treatment">Treatment</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="min-mentions-filter" class="form-label">Min Mentions</label>
                                            <select id="min-mentions-filter" class="form-select">
                                                <option value="0">0+</option>
                                                <option value="1">1+</option>
                                                <option value="2">2+</option>
                                                <option value="5">5+</option>
                                                <option value="10">10+</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="entities-loading" class="loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Loading entities...</p>
                                </div>
                                <div id="entities-list">
                                    <!-- Entities will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal fade" id="entity-details-modal" tabindex="-1" aria-labelledby="entity-details-modal-label" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="entity-details-modal-label">Entity Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="entity-details-content">
                                <!-- Entity details will be displayed here -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>Upload Documents</h5>
                                <a href="/batch-upload" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-files"></i> Switch to Batch Upload
                                </a>
                            </div>
                            <div class="card-body">
                                <div id="dropzone" class="dropzone">
                                    <div class="dropzone-content">
                                        <i class="bi bi-cloud-upload"></i>
                                        <p>Drag and drop multiple files or folders here</p>
                                        <p class="small text-muted">You can select multiple files at once by holding Ctrl (or Cmd on Mac) while selecting</p>
                                        <div class="d-flex gap-2 justify-content-center">
                                            <button type="button" class="btn btn-primary" id="select-file-button">
                                                <i class="bi bi-file-earmark me-1"></i> Select Files
                                            </button>
                                            <button type="button" class="btn btn-primary" id="select-folder-button">
                                                <i class="bi bi-folder me-1"></i> Select Folder
                                            </button>
                                        </div>
                                        <input type="file" id="file-input" accept=".pdf,.txt,.md,.rtf,.doc,.docx,.odt,.html,.htm,.xml,.csv,.xls,.xlsx,.ppt,.pptx,.epub" style="display: none;" multiple>
                                        <input type="file" id="folder-input" style="display: none;" webkitdirectory directory multiple>
                                        <div class="mt-2 text-muted small">
                                            <div id="max-file-size-info">Maximum file size: 50MB</div>
                                            <div id="supported-formats-info">Supported formats: PDF, Text, Word, HTML, CSV, Excel, PowerPoint, EPUB</div>
                                        </div>
                                    </div>
                                </div>
                                <div id="selected-files" class="mt-3">
                                    <!-- Selected files will be displayed here -->
                                </div>
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6>Processing Options</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                                                    <input type="number" class="form-control" id="chunk-size" value="1200">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="overlap" class="form-label">Overlap (characters)</label>
                                                    <input type="number" class="form-control" id="overlap" value="0">
                                                </div>
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="extract-entities" checked>
                                                    <label class="form-check-label" for="extract-entities">
                                                        Extract Entities
                                                    </label>
                                                </div>
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="extract-references" checked>
                                                    <label class="form-check-label" for="extract-references">
                                                        Extract References
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6>Upload</h6>
                                            </div>
                                            <div class="card-body">
                                                <p>Click the button below to upload and process the selected file(s).</p>
                                                <button type="button" class="btn btn-primary" id="upload-button" disabled>Upload and Process</button>
                                                <div id="upload-progress" class="mt-3" style="display: none;">
                                                    <p id="upload-status">Uploading files...</p>
                                                    <div class="progress mb-2">
                                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                </div>
                                                <div id="processing-progress" class="mt-3" style="display: none;">
                                                    <p id="processing-status">Processing document...</p>
                                                    <div class="progress mb-2">
                                                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                    <p id="processing-step" class="small text-muted">Initializing...</p>
                                                </div>

                                                <!-- Enhanced Progress Tracking UI -->
                                                {% include 'enhanced_progress.html' %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="upload-results" class="mt-4" style="display: none;">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6>Results</h6>
                                        </div>
                                        <div class="card-body" id="upload-results-content">
                                            <!-- Upload results will be displayed here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Metadata Tab -->
            <div class="tab-pane fade" id="metadata" role="tabpanel" aria-labelledby="metadata-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Document Metadata</h5>
                            </div>
                            <div class="card-body">
                                <div id="metadata-loading" class="loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Loading metadata...</p>
                                </div>
                                <div id="metadata-list">
                                    <!-- Metadata will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- References Tab -->
            <div class="tab-pane fade" id="references" role="tabpanel" aria-labelledby="references-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Document References</h5>
                            </div>
                            <div class="card-body">
                                <!-- Filters -->
                                <div class="row mb-4" id="reference-filters">
                                    <div class="col-md-4">
                                        <label for="document-selector" class="form-label">Document</label>
                                        <select class="form-select" id="document-selector">
                                            <option value="">All Documents</option>
                                        </select>
                                    </div>
                                    <div class="col-md-8">
                                        <label for="reference-search-input" class="form-label">Search</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="reference-search-input" placeholder="Search references...">
                                            <button class="btn btn-outline-secondary" type="button" id="export-references-button" disabled>
                                                <i class="bi bi-download"></i> Export CSV
                                            </button>
                                            <button class="btn btn-outline-primary" type="button" id="visualize-references-button" disabled>
                                                <i class="bi bi-graph-up"></i> Visualize
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Statistics -->
                                <div class="row mb-4" id="reference-statistics" style="display: none;">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6>Reference Statistics</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="card bg-light">
                                                            <div class="card-body text-center">
                                                                <h3 id="total-references-count">0</h3>
                                                                <p class="mb-0">Total References</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="card bg-light">
                                                            <div class="card-body text-center">
                                                                <h3 id="documents-count">0</h3>
                                                                <p class="mb-0">Documents</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="card bg-light">
                                                            <div class="card-body text-center">
                                                                <h3 id="journals-count">0</h3>
                                                                <p class="mb-0">Journals</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="card bg-light">
                                                            <div class="card-body text-center">
                                                                <h3 id="authors-count">0</h3>
                                                                <p class="mb-0">Authors</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Loading indicator -->
                                <div id="references-loading" class="loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Loading references...</p>
                                </div>

                                <!-- References list -->
                                <div id="references-list">
                                    <!-- References will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhancements Tab -->
            <div class="tab-pane fade" id="enhancements" role="tabpanel" aria-labelledby="enhancements-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Reference Enhancements</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Deduplication</h5>
                                        <p class="card-text">Find and link duplicate references in the knowledge graph.</p>
                                        <button id="deduplicate-button" class="btn btn-primary">Run Deduplication</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Citation Network</h5>
                                        <p class="card-text">Build a citation network from references in the knowledge graph.</p>
                                        <button id="build-network-button" class="btn btn-primary">Build Network</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Bibliographic Enrichment</h5>
                                        <p class="card-text">Enrich references with data from external bibliographic databases.</p>
                                        <button id="enrich-references-button" class="btn btn-primary">Enrich References</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Run All</h5>
                                        <p class="card-text">Run all reference enhancements in sequence.</p>
                                        <button id="run-all-enhancements-button" class="btn btn-primary">Run All</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="enhancements-status">
                            <!-- Status will be displayed here -->
                        </div>

                        <div id="enhancements-progress" style="display: none;">
                            <h5 class="progress-message">Processing...</h5>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>

                        <div id="enhancements-results">
                            <!-- Results will be displayed here -->
                        </div>

                        <div id="network-visualization">
                            <!-- Network visualization will be displayed here -->
                        </div>

                        <div id="duplicate-groups-list">
                            <!-- Duplicate groups will be displayed here -->
                        </div>

                        <div id="enriched-references-list">
                            <!-- Enriched references will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graph Tab -->
            <div class="tab-pane fade" id="graph" role="tabpanel" aria-labelledby="graph-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Knowledge Graph Visualization</h5>
                            </div>
                            <div class="card-body">
                                <div id="graph-loading" class="loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Loading graph...</p>
                                </div>
                                <div id="graph-container" style="height: 600px;">
                                    <!-- Graph will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Settings</h5>
                            </div>
                            <div class="card-body">
                                <div id="settings-loading" class="loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Loading settings...</p>
                                </div>

                                <!-- LLM Settings -->
                                <div class="mb-4">
                                    <h5>LLM Settings</h5>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="llm-provider" class="form-label">LLM Provider</label>
                                            <select id="llm-provider" class="form-select">
                                                <option value="openrouter">OpenRouter</option>
                                                <option value="ollama">Ollama (Local)</option>
                                                <option value="openai">OpenAI</option>
                                                <option value="gemini">Google Gemini</option>
                                                <option value="mistral">Mistral</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="llm-model" class="form-label">LLM Model</label>
                                            <select id="llm-model" class="form-select">
                                                <!-- Options will be populated dynamically -->
                                            </select>
                                        </div>
                                    </div>
                                    <button id="save-llm-settings" class="btn btn-primary">Save LLM Settings</button>
                                </div>

                                <!-- Embedding Settings -->
                                <div class="mb-4">
                                    <h5>Embedding Settings</h5>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="embedding-provider" class="form-label">Embedding Provider</label>
                                            <select id="embedding-provider" class="form-select">
                                                <option value="openai">OpenAI</option>
                                                <option value="local" selected>Local (Ollama)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="embedding-model" class="form-label">Embedding Model</label>
                                            <select id="embedding-model" class="form-select" disabled>
                                                <option value="snowflake-arctic-embed2:latest" selected>Snowflake Arctic Embed2 (Recommended)</option>
                                            </select>
                                            <input type="hidden" name="embedding-model-hidden" value="snowflake-arctic-embed2:latest">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                                            <input type="number" id="chunk-size" class="form-control" value="1200">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="chunk-overlap" class="form-label">Chunk Overlap (characters)</label>
                                            <input type="number" id="chunk-overlap" class="form-control" value="0">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="recursive-chunking" class="form-label">Recursive Chunking</label>
                                            <select id="recursive-chunking" class="form-select">
                                                <option value="true">Enabled</option>
                                                <option value="false">Disabled</option>
                                            </select>
                                        </div>
                                    </div>
                                    <button id="save-embedding-settings" class="btn btn-primary">Save Embedding Settings</button>
                                </div>

                                <!-- Database Settings -->
                                <div class="mb-4">
                                    <h5>Database Settings</h5>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="database-host" class="form-label">Database Host</label>
                                            <input type="text" id="database-host" class="form-control" placeholder="localhost">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="database-port" class="form-label">Database Port</label>
                                            <input type="number" id="database-port" class="form-control" placeholder="7689">
                                        </div>
                                    </div>
                                    <button id="save-database-settings" class="btn btn-primary">Save Database Settings</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Showdown JS for Markdown conversion -->
    <script src="https://cdn.jsdelivr.net/npm/showdown@2.1.0/dist/showdown.min.js"></script>

    <!-- Custom JS -->
    <script src="static/add_enhancements_link.js?v=1.0.4"></script>

    <!-- New Unified UI JavaScript -->
    <script src="static/js/qa_interface.js?v=1.0.5"></script>
    <script src="static/js/graphiti_ui.js?v=1.0.0"></script>
    <script src="static/js/graphiti_ui_part2.js?v=1.0.0"></script>
    <script src="static/js/graphiti_ui_part3.js?v=1.0.0"></script>
    <script src="static/js/graphiti_ui_part4.js?v=1.0.0"></script>
    <script src="static/js/graphiti_ui_part5.js?v=1.0.0"></script>
    <script src="static/js/graphiti_ui_part6.js?v=1.0.0"></script>
    <script src="static/js/graphiti_ui_part7.js?v=1.0.0"></script>

    <!-- Enhanced Progress Tracking -->
    <link rel="stylesheet" href="static/css/enhanced_progress.css?v=1.0.0">
    <script src="static/js/enhanced_progress.js?v=1.0.0"></script>

    <!-- Settings JS -->
    <script src="static/flask_settings.js?v=1.0.0"></script>

    <!-- Legacy JS (will be removed) -->
    <script src="static/flask_upload.js?v=1.0.4"></script>
</body>
</html>

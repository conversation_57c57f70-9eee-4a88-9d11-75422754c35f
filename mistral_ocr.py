"""
Mistral OCR processor for extracting text from PDFs and images
"""

import os
import logging
import base64
import asyncio
from typing import Optional, List, Dict, Any, Union
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import required libraries
try:
    # Try different import approaches to ensure we find the module
    try:
        import mistralai
        # Check which version of mistralai we have
        if hasattr(mistralai, 'Mistral'):
            # Current version structure (1.7.0+)
            from mistralai import Mistral as MistralClient
            # Define a simple ChatMessage class for compatibility
            class ChatMessage:
                def __init__(self, role, content):
                    self.role = role
                    self.content = content
            MISTRAL_AVAILABLE = True
            logger.info("Successfully imported mistralai package (current version with Mistral class)")
        elif hasattr(mistralai, 'client') and hasattr(mistralai.client, 'MistralClient'):
            # New version structure
            from mistralai.client import MistralClient
            # Define a simple ChatMessage class for compatibility
            class ChatMessage:
                def __init__(self, role, content):
                    self.role = role
                    self.content = content
            MISTRAL_AVAILABLE = True
            logger.info("Successfully imported mistralai package (new version)")
        elif hasattr(mistralai, 'MistralClient'):
            # Old version structure
            from mistralai import MistralClient
            from mistralai.models.chat_completion import ChatMessage
            MISTRAL_AVAILABLE = True
            logger.info("Successfully imported mistralai package (old version)")
        else:
            # Unknown structure
            logger.warning("Mistralai package found but has unknown structure")
            raise ImportError("Mistralai package has unknown structure")
    except ImportError:
        # Try with subprocess to install if not available
        import subprocess
        import sys
        logger.warning("mistralai package not found, attempting to install...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "mistralai"])
            import mistralai
            # Try to import based on version
            if hasattr(mistralai, 'Mistral'):
                # Current version structure (1.7.0+)
                from mistralai import Mistral as MistralClient
                # Define a simple ChatMessage class for compatibility
                class ChatMessage:
                    def __init__(self, role, content):
                        self.role = role
                        self.content = content
                MISTRAL_AVAILABLE = True
                logger.info("Successfully installed and imported mistralai package (current version with Mistral class)")
            elif hasattr(mistralai, 'client') and hasattr(mistralai.client, 'MistralClient'):
                # New version structure
                from mistralai.client import MistralClient
                # Define a simple ChatMessage class for compatibility
                class ChatMessage:
                    def __init__(self, role, content):
                        self.role = role
                        self.content = content
                MISTRAL_AVAILABLE = True
                logger.info("Successfully installed and imported mistralai package (new version)")
            elif hasattr(mistralai, 'MistralClient'):
                # Old version structure
                from mistralai import MistralClient
                from mistralai.models.chat_completion import ChatMessage
                MISTRAL_AVAILABLE = True
                logger.info("Successfully installed and imported mistralai package (old version)")
            else:
                # Unknown structure
                logger.warning("Installed mistralai package but has unknown structure")
                raise ImportError("Installed mistralai package has unknown structure")
        except Exception as e:
            logger.error(f"Failed to install mistralai package: {e}")
            raise ImportError("Could not install mistralai package")
except ImportError:
    logger.warning("mistralai package not available. Mistral OCR will not work.")
    MISTRAL_AVAILABLE = False

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    logger.warning("PyPDF2 not available. PDF handling will be limited.")
    PYPDF2_AVAILABLE = False

class MistralOCRProcessor:
    """
    Class for processing PDFs and images using Mistral's OCR capabilities
    """

    def __init__(self, api_key: str = None, model: str = "mistral-ocr-latest"):
        """
        Initialize the Mistral OCR processor

        Args:
            api_key (str): Mistral API key
            model (str): Mistral model to use for OCR
        """
        # Check if Mistral is available, but don't raise an error
        # This allows the class to be instantiated even if Mistral is not available
        # The actual OCR functions will handle the unavailability
        self.mistral_available = MISTRAL_AVAILABLE
        self.api_key = api_key
        self.model = model
        self.client = None

        if MISTRAL_AVAILABLE and api_key:
            try:
                self.client = MistralClient(api_key=api_key)
                logger.info(f"Initialized MistralOCRProcessor with model: {model}")
            except Exception as e:
                logger.error(f"Failed to initialize MistralClient: {e}")
                self.mistral_available = False

    async def extract_text_from_pdf(self, pdf_path: str, max_pages: Optional[int] = None) -> str:
        """
        Extract text from a PDF file using Mistral OCR

        Args:
            pdf_path (str): Path to the PDF file
            max_pages (int, optional): Maximum number of pages to process

        Returns:
            str: Extracted text from the PDF
        """
        if not os.path.exists(pdf_path):
            logger.error(f"PDF file not found: {pdf_path}")
            return ""

        # If Mistral is not available or client initialization failed, return empty string
        if not self.mistral_available or not self.client:
            logger.warning("Mistral OCR is not available. Cannot extract text from PDF.")
            return ""

        # Get number of pages in the PDF
        num_pages = 0
        if PYPDF2_AVAILABLE:
            try:
                with open(pdf_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    num_pages = len(reader.pages)
            except Exception as e:
                logger.error(f"Error reading PDF with PyPDF2: {e}")
                return ""
        else:
            logger.error("PyPDF2 is required for PDF processing")
            return ""

        if max_pages is not None:
            num_pages = min(num_pages, max_pages)

        logger.info(f"Extracting text from {num_pages} pages using Mistral OCR")

        # Read the PDF file as binary
        with open(pdf_path, 'rb') as file:
            pdf_data = file.read()

        # Encode the PDF data as base64
        pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')

        # Create the prompt for Mistral OCR
        system_prompt = """
        You are an OCR system that extracts text from PDF documents with a special focus on academic and scientific content.
        Extract all text content from the provided PDF, maintaining the original structure as much as possible.
        Include all text, tables, headers, footers, and any other textual content.

        Pay special attention to:
        1. Reference citations in any format (e.g., [1], (Smith et al., 2020), etc.)
        2. Bibliographic entries that may appear anywhere in the document, not just in a dedicated "References" section
        3. Citations with DOIs, PMIDs, or other identifiers
        4. Numbered reference lists (e.g., "1. Author et al. Title...")
        5. References following headers like "References", "Bibliography", "Sources", etc.
        6. References embedded within slides, presentations, or non-traditional document formats

        Preserve the exact formatting of all references and citations to ensure they can be properly extracted later.
        """

        user_prompt = f"""
        Please extract all text from the attached PDF document.
        The document has {num_pages} pages.

        Make sure to capture ALL references and citations, even if they appear in unusual locations or formats.
        Common reference formats include:
        - Numbered references: [1] or (1) or 1.
        - Author-year citations: (Smith et al., 2020)
        - References with DOIs: doi: 10.1234/example
        - References with PMIDs: PMID: 12345678
        - Full bibliographic entries anywhere in the document
        """

        # Create the messages for the chat completion
        messages = [
            ChatMessage(role="system", content=system_prompt),
            ChatMessage(role="user", content=[
                {"type": "text", "text": user_prompt},
                {"type": "image", "image": pdf_base64}
            ])
        ]

        try:
            # Call the Mistral API - handle different API versions
            try:
                # Try new API format first
                response = self.client.chat(
                    model=self.model,
                    messages=[{"role": msg.role, "content": msg.content} for msg in messages],
                    max_tokens=8000  # Adjust as needed
                )

                # Extract the text from the response
                if hasattr(response, 'choices') and hasattr(response.choices[0], 'message'):
                    extracted_text = response.choices[0].message.content
                else:
                    # Handle different response format
                    extracted_text = response.content

            except (AttributeError, TypeError):
                # Try old API format
                response = self.client.chat(
                    model=self.model,
                    messages=messages,
                    max_tokens=8000  # Adjust as needed
                )

                # Extract the text from the response
                extracted_text = response.choices[0].message.content

            logger.info(f"Successfully extracted {len(extracted_text)} characters with Mistral OCR")
            return extracted_text
        except Exception as e:
            logger.error(f"Error extracting text with Mistral OCR: {e}")
            return ""

    async def extract_text_from_image(self, image_path: str) -> str:
        """
        Extract text from an image file using Mistral OCR

        Args:
            image_path (str): Path to the image file

        Returns:
            str: Extracted text from the image
        """
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return ""

        # If Mistral is not available or client initialization failed, return empty string
        if not self.mistral_available or not self.client:
            logger.warning("Mistral OCR is not available. Cannot extract text from image.")
            return ""

        logger.info(f"Extracting text from image using Mistral OCR: {image_path}")

        # Read the image file as binary
        with open(image_path, 'rb') as file:
            image_data = file.read()

        # Encode the image data as base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Create the prompt for Mistral OCR
        system_prompt = """
        You are an OCR system that extracts text from images with a special focus on academic and scientific content.
        Extract all text content from the provided image, maintaining the original structure as much as possible.
        Include all text, tables, headers, footers, and any other textual content.

        Pay special attention to:
        1. Reference citations in any format (e.g., [1], (Smith et al., 2020), etc.)
        2. Bibliographic entries that may appear anywhere in the image, not just in a dedicated "References" section
        3. Citations with DOIs, PMIDs, or other identifiers
        4. Numbered reference lists (e.g., "1. Author et al. Title...")
        5. References following headers like "References", "Bibliography", "Sources", etc.
        6. References embedded within slides, presentations, or non-traditional document formats

        Preserve the exact formatting of all references and citations to ensure they can be properly extracted later.
        """

        user_prompt = """
        Please extract all text from the attached image.

        Make sure to capture ALL references and citations, even if they appear in unusual locations or formats.
        Common reference formats include:
        - Numbered references: [1] or (1) or 1.
        - Author-year citations: (Smith et al., 2020)
        - References with DOIs: doi: 10.1234/example
        - References with PMIDs: PMID: 12345678
        - Full bibliographic entries anywhere in the image
        """

        # Create the messages for the chat completion
        messages = [
            ChatMessage(role="system", content=system_prompt),
            ChatMessage(role="user", content=[
                {"type": "text", "text": user_prompt},
                {"type": "image", "image": image_base64}
            ])
        ]

        try:
            # Call the Mistral API - handle different API versions
            try:
                # Try new API format first
                response = self.client.chat(
                    model=self.model,
                    messages=[{"role": msg.role, "content": msg.content} for msg in messages],
                    max_tokens=4000  # Adjust as needed
                )

                # Extract the text from the response
                if hasattr(response, 'choices') and hasattr(response.choices[0], 'message'):
                    extracted_text = response.choices[0].message.content
                else:
                    # Handle different response format
                    extracted_text = response.content

            except (AttributeError, TypeError):
                # Try old API format
                response = self.client.chat(
                    model=self.model,
                    messages=messages,
                    max_tokens=4000  # Adjust as needed
                )

                # Extract the text from the response
                extracted_text = response.choices[0].message.content

            logger.info(f"Successfully extracted {len(extracted_text)} characters with Mistral OCR")
            return extracted_text
        except Exception as e:
            logger.error(f"Error extracting text with Mistral OCR: {e}")
            return ""

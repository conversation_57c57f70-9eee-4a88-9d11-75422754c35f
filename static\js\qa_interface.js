/**
 * Q&A Interface for Graphiti Knowledge Graph
 *
 * This script handles the question answering functionality, including:
 * - Submitting questions to the API
 * - Displaying answers with references
 * - Managing conversation history
 */

// Global variables
let conversationHistory = [];
let currentLLMSettings = {
    provider: 'ollama',
    model: 'meditron:7b',
    temperature: 0.3,
    max_tokens: 1000,
    top_k: 40,
    top_p: 0.9
};

// DOM elements
document.addEventListener('DOMContentLoaded', function() {
    console.log("QA Interface script loaded");

    // Add welcome message
    addMessage('assistant', 'Hello! I am <PERSON>, your AI assistant. Ask me a question about natural medicine and health.');

    // Add event listener for the question button
    const questionButton = document.getElementById('question-button');
    if (questionButton) {
        questionButton.addEventListener('click', function() {
            askQuestion();
        });
    } else {
        console.error("Question button not found");
    }

    // Add event listener for Enter key in the input field
    const questionInput = document.getElementById('question-input');
    if (questionInput) {
        questionInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                askQuestion();
            }
        });
    } else {
        console.error("Question input not found");
    }

    // Add event listener for the clear chat button
    const clearChatButton = document.getElementById('clear-chat-button');
    if (clearChatButton) {
        clearChatButton.addEventListener('click', function() {
            clearChat();
        });
    }

    // Initialize LLM settings
    initializeLLMSettings();

    // Add event listeners for settings
    setupSettingsEventListeners();
});

/**
 * Ask a question and get an answer
 */
function askQuestion() {
    // Get the question from the input field
    const questionInput = document.getElementById('question-input');
    if (!questionInput) {
        console.error("Question input element not found");
        return;
    }

    const question = questionInput.value.trim();

    // If the question is empty, do nothing
    if (!question) {
        return;
    }

    // Add the user's question to the conversation
    addMessage('user', question);

    // Clear the input field
    questionInput.value = '';

    // Show the loading indicator
    const loadingElement = document.getElementById('answer-loading');
    if (loadingElement) {
        loadingElement.style.display = 'block';
    }

    // Hide the sources
    const sourcesElement = document.getElementById('answer-sources');
    if (sourcesElement) {
        sourcesElement.style.display = 'none';
    }

    // Build the context from conversation history
    let context = '';
    if (conversationHistory.length > 0) {
        // Get the last 3 exchanges (6 messages) for context
        const recentHistory = conversationHistory.slice(-6);
        for (const msg of recentHistory) {
            context += `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}\n`;
        }
    }

    // Call the API to get an answer
    fetch('/api/qa/answer', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            question: question,
            context: context
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Hide the loading indicator
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Log the response data for debugging
        console.log("Response data:", data);

        // Add the answer to the conversation
        if (data.answer) {
            addMessage('assistant', data.answer);

            // Display sources if available
            if (data.sources && data.sources.length > 0) {
                console.log("Sources found:", data.sources);
                displaySources(data.sources);
            } else {
                console.log("No sources found in response");
            }
        } else {
            console.log("No answer found in response");
            addMessage('assistant', "I'm sorry, but I couldn't find an answer to your question.");
        }
    })
    .catch(error => {
        // Hide the loading indicator
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        console.error("Error:", error);
        addMessage('assistant', `I'm sorry, but there was an error processing your question: ${error.message}`);
    });
}

/**
 * Add a message to the conversation
 *
 * @param {string} role - 'user' or 'assistant'
 * @param {string} content - Message content
 */
function addMessage(role, content) {
    // Add to conversation history
    conversationHistory.push({ role, content });

    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `message ${role}-message`;

    // Create message content
    const contentElement = document.createElement('div');
    contentElement.className = 'message-content';

    // Format the content with line breaks
    const formattedContent = content.replace(/\n/g, '<br>');
    contentElement.innerHTML = formattedContent;

    messageElement.appendChild(contentElement);

    // Add to conversation container
    const conversationHistoryElement = document.getElementById('conversation-history');
    if (conversationHistoryElement) {
        conversationHistoryElement.appendChild(messageElement);

        // Scroll to bottom
        const conversationContainer = document.getElementById('conversation-container');
        if (conversationContainer) {
            conversationContainer.scrollTop = conversationContainer.scrollHeight;
        }
    } else {
        console.error("Conversation history element not found");
    }
}

/**
 * Display sources for the answer
 *
 * @param {Array} sources - List of sources
 */
function displaySources(sources) {
    const sourcesElement = document.getElementById('answer-sources');
    if (!sourcesElement) {
        console.error("Sources element not found");
        return;
    }

    // Clear previous sources
    sourcesElement.innerHTML = '';

    // Create sources list
    const sourcesList = document.createElement('div');
    sourcesList.className = 'sources-list';

    // Add heading
    const heading = document.createElement('h5');
    heading.textContent = 'Sources';
    sourcesList.appendChild(heading);

    // Log sources for debugging
    console.log("Displaying sources:", sources);

    // Add each source with its original reference number
    sources.forEach((source, index) => {
        const sourceItem = document.createElement('div');
        sourceItem.className = 'source-item';

        // Create source header
        const sourceHeader = document.createElement('div');
        sourceHeader.className = 'source-header';

        // Use the reference number from the source if available, otherwise use the index + 1
        // This assumes the sources are returned in the order they are referenced in the answer
        const sourceNumber = source.reference_number || (index + 1);

        // Add source number
        const sourceNumberSpan = document.createElement('span');
        sourceNumberSpan.className = 'source-number';
        sourceNumberSpan.textContent = `${sourceNumber}. `;
        sourceHeader.appendChild(sourceNumberSpan);

        // Add document title
        const sourceTitle = document.createElement('strong');
        let titleText = 'Unknown document';

        // Use document_title if available
        if (source.document_title && source.document_title.trim() !== '') {
            titleText = source.document_title;
        }
        // If document_title is not available but document_id is, use it as part of the title
        else if (source.document_id) {
            // Try to extract a meaningful title from the document ID
            // If it's a UUID, format it nicely
            if (source.document_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
                // Just use the first 8 characters of the UUID for brevity
                titleText = `Document ${source.document_id.substring(0, 8)}`;
            } else {
                titleText = `Document ${source.document_id}`;
            }
        }

        // Remove any leading numbers from the title (like "1 Herbal Medicine...")
        titleText = titleText.replace(/^\d+\s+/, '');

        sourceTitle.textContent = titleText;
        sourceHeader.appendChild(sourceTitle);

        // Add document ID if available (in a more compact format)
        if (source.document_id) {
            const sourceId = document.createElement('span');
            sourceId.className = 'source-id';
            // Format the UUID to be more readable
            if (source.document_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
                // Just show the first part of the UUID
                sourceId.textContent = ` (ID: ${source.document_id.substring(0, 8)}...)`;
            } else {
                sourceId.textContent = ` (ID: ${source.document_id})`;
            }
            sourceHeader.appendChild(sourceId);
        }

        sourceItem.appendChild(sourceHeader);

        // Add source metadata if available
        if (source.metadata) {
            const metadataDiv = document.createElement('div');
            metadataDiv.className = 'source-metadata';

            // Format metadata
            let metadataText = '';
            if (source.metadata.author) metadataText += `Author: ${source.metadata.author} `;
            if (source.metadata.year) metadataText += `Year: ${source.metadata.year} `;
            if (source.metadata.journal) metadataText += `Journal: ${source.metadata.journal} `;
            if (source.metadata.doi) metadataText += `DOI: ${source.metadata.doi}`;

            metadataDiv.textContent = metadataText.trim();
            if (metadataText.trim() !== '') {
                sourceItem.appendChild(metadataDiv);
            }
        }

        // Add source preview
        const sourcePreview = document.createElement('div');
        sourcePreview.className = 'source-preview';

        // Handle preview content
        if (source.preview && source.preview.trim() !== '') {
            // Clean up the preview text
            let previewText = source.preview;

            // Replace multiple spaces with a single space
            previewText = previewText.replace(/\s+/g, ' ');

            // Replace newlines with <br> tags
            previewText = previewText.replace(/\n/g, '<br>');

            // Format citations like [1], [2], etc.
            previewText = previewText.replace(/\[(\d+)\]/g, '<span class="citation">[$1]</span>');

            // Format journal names and dates
            previewText = previewText.replace(/(Journal|Cancer Research|Epidemiol|Medical Hypothesis)(\s+)(\([^)]+\))/g,
                '<em>$1</em>$2<strong>$3</strong>');

            // Format section headers
            previewText = previewText.replace(/##\s+([^\n]+)/g, '<h4>$1</h4>');

            // Format bullet points
            previewText = previewText.replace(/-\s+([^\n]+)/g, '• $1<br>');

            // Format mathematical notation
            previewText = previewText.replace(/\$([^$]+)\$/g, '<span class="math-notation">$1</span>');

            // Handle special characters and escape sequences
            previewText = previewText.replace(/\\&/g, '&amp;');
            previewText = previewText.replace(/\\%/g, '%');

            // Clean up any OCR artifacts or code elements
            previewText = previewText.replace(/OCRPageDimensions\([^)]+\)/g, '');
            previewText = previewText.replace(/OCRPageObject\([^"]+markdown="/g, '');
            previewText = previewText.replace(/images=\[\],\s*dimensions=/g, '');

            sourcePreview.innerHTML = previewText;
        } else {
            sourcePreview.textContent = 'No preview available';
        }

        sourceItem.appendChild(sourcePreview);

        // Add to sources list
        sourcesList.appendChild(sourceItem);
    });

    // Add to sources element
    sourcesElement.appendChild(sourcesList);
    sourcesElement.style.display = 'block';
}

/**
 * Clear the chat history
 */
function clearChat() {
    // Clear conversation history
    conversationHistory = [];

    // Clear conversation container
    const conversationHistoryElement = document.getElementById('conversation-history');
    if (conversationHistoryElement) {
        conversationHistoryElement.innerHTML = '';

        // Add welcome message
        addMessage('assistant', 'Hello! I am Avery, your AI assistant. Ask me a question about natural medicine and health.');
    } else {
        console.error("Conversation history element not found");
    }

    // Hide sources
    const sourcesElement = document.getElementById('answer-sources');
    if (sourcesElement) {
        sourcesElement.style.display = 'none';
    }
}

/**
 * Initialize LLM settings
 */
async function initializeLLMSettings() {
    try {
        // Load current settings from the server
        const response = await fetch('/api/settings');
        if (response.ok) {
            const settings = await response.json();

            // Update current settings
            if (settings.llm) {
                currentLLMSettings.provider = settings.llm.provider || 'ollama';
                currentLLMSettings.model = settings.llm.model || 'meditron:7b';
            }

            // Update UI elements
            updateCurrentModelDisplay();
            loadSettingsIntoUI();
            loadAvailableModels();
        }
    } catch (error) {
        console.error('Error loading LLM settings:', error);
    }
}

/**
 * Setup event listeners for settings controls
 */
function setupSettingsEventListeners() {
    // Provider change
    const providerSelect = document.getElementById('qa-llm-provider');
    if (providerSelect) {
        providerSelect.addEventListener('change', function() {
            currentLLMSettings.provider = this.value;
            loadAvailableModels();
            toggleAdvancedSettings();
        });
    }

    // Model change
    const modelSelect = document.getElementById('qa-llm-model');
    if (modelSelect) {
        modelSelect.addEventListener('change', function() {
            currentLLMSettings.model = this.value;
            updateCurrentModelDisplay();
        });
    }

    // Temperature slider
    const temperatureSlider = document.getElementById('qa-temperature');
    const temperatureValue = document.getElementById('temperature-value');
    if (temperatureSlider && temperatureValue) {
        temperatureSlider.addEventListener('input', function() {
            currentLLMSettings.temperature = parseFloat(this.value);
            temperatureValue.textContent = this.value;
        });
    }

    // Max tokens slider
    const maxTokensSlider = document.getElementById('qa-max-tokens');
    const maxTokensValue = document.getElementById('max-tokens-value');
    if (maxTokensSlider && maxTokensValue) {
        maxTokensSlider.addEventListener('input', function() {
            currentLLMSettings.max_tokens = parseInt(this.value);
            maxTokensValue.textContent = this.value;
        });
    }

    // Top K slider
    const topKSlider = document.getElementById('qa-top-k');
    const topKValue = document.getElementById('top-k-value');
    if (topKSlider && topKValue) {
        topKSlider.addEventListener('input', function() {
            currentLLMSettings.top_k = parseInt(this.value);
            topKValue.textContent = this.value;
        });
    }

    // Top P slider
    const topPSlider = document.getElementById('qa-top-p');
    const topPValue = document.getElementById('top-p-value');
    if (topPSlider && topPValue) {
        topPSlider.addEventListener('input', function() {
            currentLLMSettings.top_p = parseFloat(this.value);
            topPValue.textContent = this.value;
        });
    }

    // Apply settings button
    const applyButton = document.getElementById('apply-qa-settings');
    if (applyButton) {
        applyButton.addEventListener('click', applyLLMSettings);
    }
}

/**
 * Load settings into UI controls
 */
function loadSettingsIntoUI() {
    // Set provider
    const providerSelect = document.getElementById('qa-llm-provider');
    if (providerSelect) {
        providerSelect.value = currentLLMSettings.provider;
    }

    // Set temperature
    const temperatureSlider = document.getElementById('qa-temperature');
    const temperatureValue = document.getElementById('temperature-value');
    if (temperatureSlider && temperatureValue) {
        temperatureSlider.value = currentLLMSettings.temperature;
        temperatureValue.textContent = currentLLMSettings.temperature;
    }

    // Set max tokens
    const maxTokensSlider = document.getElementById('qa-max-tokens');
    const maxTokensValue = document.getElementById('max-tokens-value');
    if (maxTokensSlider && maxTokensValue) {
        maxTokensSlider.value = currentLLMSettings.max_tokens;
        maxTokensValue.textContent = currentLLMSettings.max_tokens;
    }

    // Set top K
    const topKSlider = document.getElementById('qa-top-k');
    const topKValue = document.getElementById('top-k-value');
    if (topKSlider && topKValue) {
        topKSlider.value = currentLLMSettings.top_k;
        topKValue.textContent = currentLLMSettings.top_k;
    }

    // Set top P
    const topPSlider = document.getElementById('qa-top-p');
    const topPValue = document.getElementById('top-p-value');
    if (topPSlider && topPValue) {
        topPSlider.value = currentLLMSettings.top_p;
        topPValue.textContent = currentLLMSettings.top_p;
    }

    // Toggle advanced settings
    toggleAdvancedSettings();
}

/**
 * Update current model display
 */
function updateCurrentModelDisplay() {
    const display = document.getElementById('current-model-display');
    if (display) {
        display.textContent = `${currentLLMSettings.provider}/${currentLLMSettings.model}`;
    }
}

/**
 * Toggle advanced settings visibility
 */
function toggleAdvancedSettings() {
    const advancedSettings = document.getElementById('ollama-advanced-settings');
    if (advancedSettings) {
        if (currentLLMSettings.provider === 'ollama') {
            advancedSettings.style.display = 'block';
        } else {
            advancedSettings.style.display = 'none';
        }
    }
}

/**
 * Load available models for the selected provider
 */
async function loadAvailableModels() {
    const modelSelect = document.getElementById('qa-llm-model');
    if (!modelSelect) return;

    try {
        // Clear current options
        modelSelect.innerHTML = '<option value="">Loading models...</option>';

        // Load models based on provider
        let models = [];
        if (currentLLMSettings.provider === 'ollama') {
            // Load Ollama models
            const response = await fetch('/api/ollama/models');
            if (response.ok) {
                const data = await response.json();
                models = data.models || [];
            }
        } else if (currentLLMSettings.provider === 'openrouter') {
            // Load OpenRouter models
            models = [
                'meta-llama/llama-4-maverick',
                'qwen3-4b',
                'mistralai/mistral-nemo',
                'google/gemma-3-27b-it',
                'anthropic/claude-3-sonnet-20240229'
            ];
        } else if (currentLLMSettings.provider === 'openai') {
            // Load OpenAI models
            models = ['gpt-4o', 'gpt-4', 'gpt-3.5-turbo'];
        }

        // Populate dropdown
        modelSelect.innerHTML = '';
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            if (model === currentLLMSettings.model) {
                option.selected = true;
            }
            modelSelect.appendChild(option);
        });

        // If current model is not in the list, add it
        if (!models.includes(currentLLMSettings.model) && currentLLMSettings.model) {
            const option = document.createElement('option');
            option.value = currentLLMSettings.model;
            option.textContent = currentLLMSettings.model;
            option.selected = true;
            modelSelect.appendChild(option);
        }

    } catch (error) {
        console.error('Error loading models:', error);
        modelSelect.innerHTML = '<option value="">Error loading models</option>';
    }
}

/**
 * Apply LLM settings
 */
async function applyLLMSettings() {
    try {
        const response = await fetch('/api/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                qa_llm_provider: currentLLMSettings.provider,
                qa_llm_model: currentLLMSettings.model,
                qa_llm_temperature: currentLLMSettings.temperature,
                qa_llm_max_tokens: currentLLMSettings.max_tokens,
                qa_llm_top_k: currentLLMSettings.top_k,
                qa_llm_top_p: currentLLMSettings.top_p
            })
        });

        if (response.ok) {
            // Show success message
            showSettingsStatus('Settings applied successfully!', 'success');
            updateCurrentModelDisplay();
        } else {
            showSettingsStatus('Error applying settings', 'error');
        }
    } catch (error) {
        console.error('Error applying settings:', error);
        showSettingsStatus('Error applying settings', 'error');
    }
}

/**
 * Show settings status message
 */
function showSettingsStatus(message, type) {
    const statusDiv = document.getElementById('qa-settings-status');
    if (statusDiv) {
        statusDiv.innerHTML = `
            <div class="alert alert-${type === 'success' ? 'success' : 'danger'} alert-sm" role="alert">
                ${message}
            </div>
        `;
        statusDiv.style.display = 'block';
        statusDiv.classList.add('settings-applied');

        // Hide after 3 seconds
        setTimeout(() => {
            statusDiv.style.display = 'none';
            statusDiv.classList.remove('settings-applied');
        }, 3000);
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q&A - Graphiti Knowledge Graph</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">

    <!-- Q&A Specific Styles -->
    <link rel="stylesheet" href="/static/css/qa_styles.css">
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graphiti Knowledge Graph Q&A</h1>

        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Q&A</li>
            </ol>
        </nav>

        <!-- Q&A Interface with Sidebar -->
        <div class="row">
            <!-- Main Q&A Area -->
            <div class="col-lg-9">
                <!-- Question Input -->
                <div class="row mb-4">
                    <div class="col-md-10">
                        <div class="input-group">
                            <input type="text" id="question-input" class="form-control" placeholder="Ask a question...">
                            <button class="btn btn-primary" type="button" id="question-button">Ask</button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" type="button" id="clear-chat-button">Clear Chat</button>
                    </div>
                </div>

                <!-- Conversation Area -->
                <div id="conversation-container" class="mb-4" style="max-height: 500px; overflow-y: auto;">
                    <div id="conversation-history"></div>
                </div>

                <!-- Loading Indicator -->
                <div id="answer-loading" class="loading" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Generating answer...</p>
                </div>

                <!-- Sources -->
                <div id="answer-sources" style="display: none;">
                    <div id="sources-list"></div>
                </div>
            </div>

            <!-- LLM Settings Sidebar -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog"></i> LLM Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Current Model Display -->
                        <div class="mb-3">
                            <label class="form-label"><strong>Current Model:</strong></label>
                            <div id="current-model-display" class="text-muted small">
                                Loading...
                            </div>
                        </div>

                        <!-- Model Selection -->
                        <div class="mb-3">
                            <label for="qa-llm-provider" class="form-label">Provider</label>
                            <select class="form-select form-select-sm" id="qa-llm-provider">
                                <option value="ollama">Ollama (Local)</option>
                                <option value="openrouter">OpenRouter</option>
                                <option value="openai">OpenAI</option>
                                <option value="gemini">Google Gemini</option>
                                <option value="mistral">Mistral</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="qa-llm-model" class="form-label">Model</label>
                            <select class="form-select form-select-sm" id="qa-llm-model">
                                <option value="">Loading models...</option>
                            </select>
                        </div>

                        <!-- Temperature Setting -->
                        <div class="mb-3">
                            <label for="qa-temperature" class="form-label">
                                Temperature: <span id="temperature-value">0.3</span>
                            </label>
                            <input type="range" class="form-range" id="qa-temperature"
                                   min="0" max="1" step="0.1" value="0.3">
                            <div class="form-text">Lower = more focused, Higher = more creative</div>
                        </div>

                        <!-- Max Tokens Setting -->
                        <div class="mb-3">
                            <label for="qa-max-tokens" class="form-label">
                                Max Tokens: <span id="max-tokens-value">1000</span>
                            </label>
                            <input type="range" class="form-range" id="qa-max-tokens"
                                   min="100" max="4000" step="100" value="1000">
                            <div class="form-text">Maximum response length</div>
                        </div>

                        <!-- Advanced Settings (Ollama only) -->
                        <div id="ollama-advanced-settings" style="display: none;">
                            <hr>
                            <h6>Advanced Settings</h6>

                            <div class="mb-3">
                                <label for="qa-top-k" class="form-label">
                                    Top K: <span id="top-k-value">40</span>
                                </label>
                                <input type="range" class="form-range" id="qa-top-k"
                                       min="1" max="100" step="1" value="40">
                                <div class="form-text">Limits token choices</div>
                            </div>

                            <div class="mb-3">
                                <label for="qa-top-p" class="form-label">
                                    Top P: <span id="top-p-value">0.9</span>
                                </label>
                                <input type="range" class="form-range" id="qa-top-p"
                                       min="0.1" max="1.0" step="0.1" value="0.9">
                                <div class="form-text">Nucleus sampling threshold</div>
                            </div>
                        </div>

                        <!-- Apply Settings Button -->
                        <div class="d-grid">
                            <button class="btn btn-success btn-sm" id="apply-qa-settings">
                                <i class="fas fa-check"></i> Apply Settings
                            </button>
                        </div>

                        <!-- Settings Status -->
                        <div id="qa-settings-status" class="mt-2" style="display: none;">
                            <div class="alert alert-success alert-sm" role="alert">
                                Settings applied successfully!
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Showdown JS for Markdown conversion -->
    <script src="https://cdn.jsdelivr.net/npm/showdown@2.1.0/dist/showdown.min.js"></script>

    <!-- Custom JS -->
    <script src="static/js/qa_interface.js"></script>

    <!-- Test Script -->
    <script src="static/js/qa_test.js"></script>
</body>
</html>
